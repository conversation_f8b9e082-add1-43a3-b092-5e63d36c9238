-- =====================================================
-- CRYPTO PAYMENT GATEWAY DATABASE SCHEMA
-- Separate system for API-based business integrations
-- (Different from restaurant QR code merchants)
-- =====================================================

-- Payment Gateway Merchants (separate from restaurant merchants)
CREATE TABLE IF NOT EXISTS payment_gateway_merchants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Business Information
  business_name VARCHAR(255) NOT NULL,
  business_type VARCHAR(50) NOT NULL, -- 'ecommerce', 'saas', 'freelance', etc.
  business_category VARCHAR(100),
  website_url TEXT,
  business_description TEXT,

  -- Contact Information
  contact_name VARCHAR(255) NOT NULL,
  contact_email VARCHAR(255) NOT NULL,
  contact_phone VARCHAR(50),
  business_address TEXT,
  country VARCHAR(100) DEFAULT 'Nigeria',

  -- Business Details
  monthly_volume VARCHAR(50), -- 'under_1k', '1k_10k', etc.
  integration_type VARCHAR(50) NOT NULL, -- 'api', 'hosted', 'links', 'widgets'
  settlement_preference VARCHAR(20) DEFAULT 'mixed', -- 'crypto', 'fiat', 'mixed'
  supported_currencies JSONB DEFAULT '["NGN"]'::jsonb,

  -- Technical Information
  technical_contact_email VARCHAR(255),
  webhook_url TEXT,
  has_technical_team BOOLEAN DEFAULT false,

  -- Compliance
  business_registration_number VARCHAR(100),
  tax_id VARCHAR(100),

  -- Status
  verification_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
  is_active BOOLEAN DEFAULT false,
  approved_at TIMESTAMPTZ,
  rejected_at TIMESTAMPTZ,
  rejection_reason TEXT,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(user_id),
  CHECK (verification_status IN ('pending', 'approved', 'rejected')),
  CHECK (settlement_preference IN ('crypto', 'fiat', 'mixed')),
  CHECK (integration_type IN ('api', 'hosted', 'links', 'widgets'))
);

-- Merchant API Keys for business integrations
CREATE TABLE IF NOT EXISTS merchant_api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_gateway_merchant_id UUID NOT NULL REFERENCES payment_gateway_merchants(id) ON DELETE CASCADE,
  key_id VARCHAR(50) NOT NULL UNIQUE, -- pk_live_... or pk_test_...
  key_secret VARCHAR(255) NOT NULL, -- Hashed secret key
  key_type VARCHAR(10) NOT NULL CHECK (key_type IN ('live', 'test')),
  permissions JSONB DEFAULT '["read", "write"]'::jsonb,
  is_active BOOLEAN DEFAULT true,
  last_used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(payment_gateway_merchant_id, key_type)
);

-- Payment Intents (similar to Stripe)
CREATE TABLE IF NOT EXISTS payment_intents (
  id VARCHAR(50) PRIMARY KEY, -- pi_1234567890
  payment_gateway_merchant_id UUID NOT NULL REFERENCES payment_gateway_merchants(id) ON DELETE CASCADE,
  client_secret VARCHAR(100) NOT NULL UNIQUE,
  
  -- Payment details
  amount DECIMAL(15, 2) NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  settlement_currency VARCHAR(10) NOT NULL DEFAULT 'NGN', -- 'NGN' or 'crypto'
  accepted_cryptocurrencies JSONB NOT NULL DEFAULT '["SOL", "USDC"]'::jsonb,
  
  -- Status tracking
  status VARCHAR(30) NOT NULL DEFAULT 'requires_payment_method',
  -- Possible statuses: requires_payment_method, requires_confirmation, processing, succeeded, canceled
  
  -- URLs
  success_url TEXT,
  cancel_url TEXT,
  payment_url TEXT, -- Generated payment page URL
  
  -- Payment method used
  payment_method_id VARCHAR(50), -- References actual crypto transaction
  crypto_amount DECIMAL(18, 8),
  crypto_currency VARCHAR(10),
  exchange_rate DECIMAL(15, 6),
  
  -- Metadata
  metadata JSONB DEFAULT '{}'::jsonb,
  description TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  confirmed_at TIMESTAMPTZ,
  succeeded_at TIMESTAMPTZ,
  canceled_at TIMESTAMPTZ,
  
  -- Constraints
  CHECK (status IN ('requires_payment_method', 'requires_confirmation', 'processing', 'succeeded', 'canceled', 'failed'))
);

-- Payment Methods (crypto transactions)
CREATE TABLE IF NOT EXISTS payment_methods (
  id VARCHAR(50) PRIMARY KEY, -- pm_1234567890
  payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id) ON DELETE CASCADE,
  
  -- Crypto details
  crypto_currency VARCHAR(10) NOT NULL,
  crypto_amount DECIMAL(18, 8) NOT NULL,
  wallet_address VARCHAR(255) NOT NULL, -- Customer's wallet
  recipient_address VARCHAR(255) NOT NULL, -- Our receiving wallet
  
  -- Blockchain transaction
  transaction_hash VARCHAR(255),
  block_number BIGINT,
  confirmations INTEGER DEFAULT 0,
  required_confirmations INTEGER DEFAULT 1,
  
  -- Status
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  -- Possible statuses: pending, confirmed, failed
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  confirmed_at TIMESTAMPTZ,
  
  CHECK (status IN ('pending', 'confirmed', 'failed'))
);

-- Webhook Endpoints for merchants
CREATE TABLE IF NOT EXISTS merchant_webhook_endpoints (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_gateway_merchant_id UUID NOT NULL REFERENCES payment_gateway_merchants(id) ON DELETE CASCADE,
  url TEXT NOT NULL,
  events JSONB NOT NULL DEFAULT '["payment_intent.succeeded"]'::jsonb,
  secret VARCHAR(255) NOT NULL, -- For webhook signature verification
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Webhook Delivery Attempts
CREATE TABLE IF NOT EXISTS webhook_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webhook_endpoint_id UUID NOT NULL REFERENCES merchant_webhook_endpoints(id) ON DELETE CASCADE,
  payment_intent_id VARCHAR(50) REFERENCES payment_intents(id) ON DELETE CASCADE,
  event_type VARCHAR(50) NOT NULL,
  payload JSONB NOT NULL,
  
  -- Delivery tracking
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  http_status_code INTEGER,
  response_body TEXT,
  attempt_count INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 5,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  next_attempt_at TIMESTAMPTZ DEFAULT NOW(),
  delivered_at TIMESTAMPTZ,
  
  CHECK (status IN ('pending', 'delivered', 'failed'))
);

-- Payment Links (shareable URLs)
CREATE TABLE IF NOT EXISTS payment_links (
  id VARCHAR(50) PRIMARY KEY, -- plink_1234567890
  payment_gateway_merchant_id UUID NOT NULL REFERENCES payment_gateway_merchants(id) ON DELETE CASCADE,
  
  -- Link details
  url_slug VARCHAR(100) NOT NULL UNIQUE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Payment configuration
  amount DECIMAL(15, 2),
  currency VARCHAR(3) DEFAULT 'USD',
  is_amount_fixed BOOLEAN DEFAULT true,
  min_amount DECIMAL(15, 2),
  max_amount DECIMAL(15, 2),
  accepted_cryptocurrencies JSONB DEFAULT '["SOL", "USDC"]'::jsonb,
  
  -- Settings
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMPTZ,
  max_uses INTEGER,
  current_uses INTEGER DEFAULT 0,
  
  -- Metadata
  metadata JSONB DEFAULT '{}'::jsonb,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Business Integration Settings
CREATE TABLE IF NOT EXISTS merchant_integration_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_gateway_merchant_id UUID NOT NULL REFERENCES payment_gateway_merchants(id) ON DELETE CASCADE,
  
  -- Branding
  business_logo TEXT,
  brand_color VARCHAR(7) DEFAULT '#000000',
  
  -- Payment settings
  default_currency VARCHAR(3) DEFAULT 'USD',
  supported_currencies JSONB DEFAULT '["USD", "NGN"]'::jsonb,
  default_crypto_currencies JSONB DEFAULT '["SOL", "USDC"]'::jsonb,
  settlement_preference VARCHAR(10) DEFAULT 'fiat', -- 'fiat' or 'crypto'
  
  -- Notification settings
  email_notifications BOOLEAN DEFAULT true,
  webhook_notifications BOOLEAN DEFAULT true,
  
  -- Security settings
  require_customer_email BOOLEAN DEFAULT false,
  require_customer_phone BOOLEAN DEFAULT false,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(payment_gateway_merchant_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_payment_gateway_merchants_user_id ON payment_gateway_merchants(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_gateway_merchants_verification_status ON payment_gateway_merchants(verification_status);
CREATE INDEX IF NOT EXISTS idx_payment_intents_merchant_id ON payment_intents(payment_gateway_merchant_id);
CREATE INDEX IF NOT EXISTS idx_payment_intents_status ON payment_intents(status);
CREATE INDEX IF NOT EXISTS idx_payment_intents_created_at ON payment_intents(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_methods_payment_intent_id ON payment_methods(payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_transaction_hash ON payment_methods(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_status ON webhook_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_next_attempt_at ON webhook_deliveries(next_attempt_at);

-- Add RLS policies for security
ALTER TABLE payment_gateway_merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_intents ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_webhook_endpoints ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_integration_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Payment gateway merchants can manage their own data" ON payment_gateway_merchants
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Merchants can manage their own API keys" ON merchant_api_keys
  FOR ALL USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Merchants can manage their own payment intents" ON payment_intents
  FOR ALL USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Merchants can view their own payment methods" ON payment_methods
  FOR SELECT USING (
    payment_intent_id IN (
      SELECT id FROM payment_intents WHERE payment_gateway_merchant_id IN (
        SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Merchants can manage their own webhooks" ON merchant_webhook_endpoints
  FOR ALL USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Merchants can view their own webhook deliveries" ON webhook_deliveries
  FOR SELECT USING (
    webhook_endpoint_id IN (
      SELECT id FROM merchant_webhook_endpoints WHERE payment_gateway_merchant_id IN (
        SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Merchants can manage their own payment links" ON payment_links
  FOR ALL USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Merchants can manage their own integration settings" ON merchant_integration_settings
  FOR ALL USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

-- Sample data for testing
-- Note: Sample data will be created when payment gateway merchants register
