/**
 * Payment Gateway Wallet Configuration
 * 
 * Page for merchants to configure their wallet addresses for receiving payments
 * across different blockchains (Solana, Ethereum, Polygon)
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  Wallet,
  ArrowLeft,
  CheckCircle,
  AlertTriangle,
  Copy,
  ExternalLink,
  Plus,
  Settings,
  Shield
} from 'lucide-react';

interface WalletConfig {
  id?: string;
  chain: string;
  token: string;
  wallet_address: string;
  is_active: boolean;
  created_at?: string;
}

interface ChainInfo {
  id: string;
  name: string;
  symbol: string;
  icon: string;
  color: string;
  tokens: TokenInfo[];
}

interface TokenInfo {
  symbol: string;
  name: string;
  icon: string;
  required: boolean;
}

const SUPPORTED_CHAINS: ChainInfo[] = [
  {
    id: 'solana',
    name: 'Solana',
    symbol: 'SOL',
    icon: '/solana.png',
    color: 'bg-purple-500',
    tokens: [
      { symbol: 'SOL', name: 'Solana', icon: '/solana.png', required: true },
      { symbol: 'USDC', name: 'USD Coin', icon: '/usdc.png', required: true },
      { symbol: 'USDT', name: 'Tether', icon: '/usdt.png', required: false }
    ]
  },
  {
    id: 'ethereum',
    name: 'Ethereum',
    symbol: 'ETH',
    icon: '/ethereum.png',
    color: 'bg-blue-500',
    tokens: [
      { symbol: 'ETH', name: 'Ethereum', icon: '/ethereum.png', required: true },
      { symbol: 'USDC', name: 'USD Coin', icon: '/usdc.png', required: true },
      { symbol: 'USDT', name: 'Tether', icon: '/usdt.png', required: false }
    ]
  },
  {
    id: 'polygon',
    name: 'Polygon',
    symbol: 'MATIC',
    icon: '/polygon.png',
    color: 'bg-purple-600',
    tokens: [
      { symbol: 'MATIC', name: 'Polygon', icon: '/polygon.png', required: true },
      { symbol: 'USDC', name: 'USD Coin', icon: '/usdc.png', required: true },
      { symbol: 'USDT', name: 'Tether', icon: '/usdt.png', required: false }
    ]
  }
];

const PaymentGatewayWallets: React.FC = () => {
  const [walletConfigs, setWalletConfigs] = useState<WalletConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [merchantId, setMerchantId] = useState<string | null>(null);
  const [editingWallet, setEditingWallet] = useState<{chain: string, token: string} | null>(null);
  const [walletAddress, setWalletAddress] = useState('');
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      loadMerchantAndWallets();
    }
  }, [user]);

  const loadMerchantAndWallets = async () => {
    try {
      // Get merchant ID
      const { data: merchantData, error: merchantError } = await supabase
        .from('payment_gateway_merchants')
        .select('id, business_name, verification_status')
        .eq('user_id', user?.id)
        .single();

      if (merchantError) {
        console.error('Error fetching merchant:', merchantError);
        toast({
          title: "Access Denied",
          description: "Please complete your payment gateway registration first",
          variant: "destructive",
        });
        navigate('/payment-gateway-register');
        return;
      }

      setMerchantId(merchantData.id);

      // Load wallet configurations
      const { data: walletsData, error: walletsError } = await supabase
        .from('merchant_wallet_configs')
        .select('*')
        .eq('payment_gateway_merchant_id', merchantData.id);

      if (walletsError) {
        console.error('Error fetching wallets:', walletsError);
      } else {
        setWalletConfigs(walletsData || []);
      }

    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "Failed to load wallet configurations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const saveWalletAddress = async () => {
    if (!merchantId || !editingWallet || !walletAddress.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid wallet address",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      // Check if wallet config already exists
      const existingConfig = walletConfigs.find(
        w => w.chain === editingWallet.chain && w.token === editingWallet.token
      );

      if (existingConfig) {
        // Update existing
        const { error } = await supabase
          .from('merchant_wallet_configs')
          .update({
            wallet_address: walletAddress.trim(),
            is_active: true
          })
          .eq('id', existingConfig.id);

        if (error) throw error;
      } else {
        // Create new
        const { error } = await supabase
          .from('merchant_wallet_configs')
          .insert({
            payment_gateway_merchant_id: merchantId,
            chain: editingWallet.chain,
            token: editingWallet.token,
            wallet_address: walletAddress.trim(),
            is_active: true
          });

        if (error) throw error;
      }

      toast({
        title: "Wallet Saved! 🎉",
        description: `${editingWallet.token} wallet address saved successfully`,
      });

      // Reload wallets
      await loadMerchantAndWallets();
      
      // Reset form
      setEditingWallet(null);
      setWalletAddress('');

    } catch (error) {
      console.error('Error saving wallet:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save wallet address. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const getWalletConfig = (chain: string, token: string): WalletConfig | undefined => {
    return walletConfigs.find(w => w.chain === chain && w.token === token);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: "Wallet address copied to clipboard",
    });
  };

  const validateWalletAddress = (address: string, chain: string): boolean => {
    if (!address.trim()) return false;
    
    switch (chain) {
      case 'solana':
        return address.length >= 32 && address.length <= 44;
      case 'ethereum':
      case 'polygon':
        return address.startsWith('0x') && address.length === 42;
      default:
        return false;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading wallet configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" onClick={() => navigate('/payment-gateway-dashboard')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
            <div className="flex items-center gap-4 mb-4">
              <img 
                src="/solana.png" 
                alt="Logo" 
                className="h-10 w-10"
              />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Wallet Configuration
                </h1>
                <p className="text-gray-600">
                  Configure your wallet addresses to receive payments across different blockchains
                </p>
              </div>
            </div>
          </div>

          {/* Important Notice */}
          <Alert className="mb-8 border-blue-200 bg-blue-50">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Add your wallet addresses for each token you want to accept. 
              Customers will send payments directly to these addresses. Make sure you control these wallets.
            </AlertDescription>
          </Alert>

          {/* Wallet Configuration by Chain */}
          <Tabs defaultValue="solana" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              {SUPPORTED_CHAINS.map((chain) => (
                <TabsTrigger key={chain.id} value={chain.id} className="flex items-center gap-2">
                  <img src={chain.icon} alt={chain.name} className="h-4 w-4" />
                  {chain.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {SUPPORTED_CHAINS.map((chain) => (
              <TabsContent key={chain.id} value={chain.id}>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <img src={chain.icon} alt={chain.name} className="h-6 w-6" />
                      {chain.name} Network
                    </CardTitle>
                    <CardDescription>
                      Configure your {chain.name} wallet addresses for receiving payments
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {chain.tokens.map((token) => {
                      const config = getWalletConfig(chain.id, token.symbol);
                      const isEditing = editingWallet?.chain === chain.id && editingWallet?.token === token.symbol;
                      
                      return (
                        <div key={token.symbol} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <img src={token.icon} alt={token.symbol} className="h-8 w-8" />
                              <div>
                                <h3 className="font-semibold flex items-center gap-2">
                                  {token.symbol}
                                  {token.required && <Badge variant="secondary">Required</Badge>}
                                </h3>
                                <p className="text-sm text-gray-600">{token.name}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {config ? (
                                <Badge variant="outline" className="text-green-600">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Configured
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="text-orange-600">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Not Set
                                </Badge>
                              )}
                            </div>
                          </div>

                          {config && !isEditing ? (
                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <Input
                                  value={config.wallet_address}
                                  readOnly
                                  className="font-mono text-sm"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => copyToClipboard(config.wallet_address)}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setEditingWallet({ chain: chain.id, token: token.symbol });
                                    setWalletAddress(config.wallet_address);
                                  }}
                                >
                                  <Settings className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ) : isEditing ? (
                            <div className="space-y-3">
                              <div>
                                <Label htmlFor={`wallet-${chain.id}-${token.symbol}`}>
                                  {chain.name} Wallet Address
                                </Label>
                                <Input
                                  id={`wallet-${chain.id}-${token.symbol}`}
                                  value={walletAddress}
                                  onChange={(e) => setWalletAddress(e.target.value)}
                                  placeholder={`Enter your ${chain.name} wallet address`}
                                  className="font-mono"
                                />
                                {walletAddress && !validateWalletAddress(walletAddress, chain.id) && (
                                  <p className="text-sm text-red-600 mt-1">
                                    Invalid {chain.name} wallet address format
                                  </p>
                                )}
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  onClick={saveWalletAddress}
                                  disabled={saving || !validateWalletAddress(walletAddress, chain.id)}
                                >
                                  {saving ? 'Saving...' : 'Save Address'}
                                </Button>
                                <Button
                                  variant="outline"
                                  onClick={() => {
                                    setEditingWallet(null);
                                    setWalletAddress('');
                                  }}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              onClick={() => {
                                setEditingWallet({ chain: chain.id, token: token.symbol });
                                setWalletAddress('');
                              }}
                              className="w-full"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add {token.symbol} Wallet Address
                            </Button>
                          )}
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>

          {/* Next Steps */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Next Steps</CardTitle>
              <CardDescription>
                After configuring your wallets, you can start accepting payments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button onClick={() => navigate('/payment-gateway-payment-links')} className="justify-start">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Create Payment Links
                </Button>
                <Button onClick={() => navigate('/merchant-api-keys')} variant="outline" className="justify-start">
                  <Settings className="h-4 w-4 mr-2" />
                  Manage API Keys
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PaymentGatewayWallets;
