/**
 * Payment Gateway Dashboard
 * 
 * Dashboard for payment gateway merchants to manage their business,
 * view analytics, and access integration tools
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  CreditCard,
  Key,
  Link,
  BarChart3,
  Settings,
  Globe,
  Code,
  Zap,
  Shield,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  ExternalLink,
  Copy,
  Plus,
  Eye,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface PaymentGatewayMerchant {
  id: string;
  business_name: string;
  business_type: string;
  verification_status: string;
  is_active: boolean;
  created_at: string;
  integration_types: string[];
  settlement_preference: string;
  website_url?: string;
}

const PaymentGatewayDashboard: React.FC = () => {
  const [merchant, setMerchant] = useState<PaymentGatewayMerchant | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total_payments: 0,
    total_volume: 0,
    successful_payments: 0,
    pending_payments: 0
  });
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      fetchMerchantData();
    }
  }, [user]);

  const fetchMerchantData = async () => {
    try {
      // Fetch payment gateway merchant data
      const { data: merchantData, error: merchantError } = await supabase
        .from('payment_gateway_merchants')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (merchantError) {
        console.error('Error fetching merchant data:', merchantError);
        if (merchantError.code === 'PGRST116') {
          // No merchant found - redirect to registration
          toast({
            title: "Registration Required",
            description: "Please complete your payment gateway registration first",
          });
          navigate('/payment-gateway-register');
          return;
        }
        throw merchantError;
      }

      setMerchant(merchantData);

      // Fetch payment statistics
      if (merchantData.id) {
        const { data: paymentIntents, error: statsError } = await supabase
          .from('payment_intents')
          .select('amount, status, currency')
          .eq('payment_gateway_merchant_id', merchantData.id);

        if (!statsError && paymentIntents) {
          const stats = paymentIntents.reduce((acc, payment) => {
            acc.total_payments++;
            acc.total_volume += payment.amount || 0;
            if (payment.status === 'succeeded') {
              acc.successful_payments++;
            } else if (payment.status === 'requires_payment_method' || payment.status === 'processing') {
              acc.pending_payments++;
            }
            return acc;
          }, {
            total_payments: 0,
            total_volume: 0,
            successful_payments: 0,
            pending_payments: 0
          });

          setStats(stats);
        }
      }

    } catch (error) {
      console.error('Error fetching merchant data:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>;
      case 'pending':
        return <Badge variant="outline"><Clock className="h-3 w-3 mr-1" />Pending Review</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const quickActions = [
    {
      title: 'API Keys',
      description: 'Manage your test and live API keys',
      icon: Key,
      href: '/merchant-api-keys',
      color: 'bg-blue-500'
    },
    {
      title: 'Payment Intents',
      description: 'Create and manage payment requests',
      icon: CreditCard,
      href: '/merchant-payment-intents',
      color: 'bg-green-500'
    },
    {
      title: 'Payment Links',
      description: 'Generate shareable payment URLs',
      icon: Link,
      href: '/merchant-payment-links',
      color: 'bg-purple-500'
    },
    {
      title: 'Integration Guide',
      description: 'View documentation and examples',
      icon: Code,
      href: '/integration-guide',
      color: 'bg-orange-500'
    },
    {
      title: 'Demo & Testing',
      description: 'Test your integration',
      icon: Activity,
      href: '/demo',
      color: 'bg-pink-500'
    },
    {
      title: 'Webhooks',
      description: 'Configure payment notifications',
      icon: Zap,
      href: '/merchant-webhooks',
      color: 'bg-yellow-500'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!merchant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-gray-900 mb-2">Registration Required</h2>
            <p className="text-gray-600 mb-6">
              You need to register as a payment gateway merchant to access this dashboard.
            </p>
            <Button onClick={() => navigate('/payment-gateway-register')}>
              Complete Registration
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-4 mb-4">
                  <img
                    src="/solana.png"
                    alt="Logo"
                    className="h-12 w-12"
                  />
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      Payment Gateway Dashboard
                    </h1>
                    <p className="text-gray-600">
                      Welcome back, {merchant.business_name}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                {getStatusBadge(merchant.verification_status)}
                <Button variant="outline" onClick={() => navigate('/integration-guide')}>
                  <Code className="h-4 w-4 mr-2" />
                  View Docs
                </Button>
              </div>
            </div>
          </div>

          {/* Status Alert */}
          {merchant.verification_status === 'pending' && (
            <Card className="mb-6 border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <div>
                    <h3 className="font-semibold text-orange-900">Account Under Review</h3>
                    <p className="text-sm text-orange-700">
                      Your payment gateway account is being reviewed. You can test with demo mode while we verify your business.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Payments</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total_payments}</p>
                  </div>
                  <CreditCard className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Volume</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_volume)}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Successful</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.successful_payments}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.pending_payments}</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and tools for managing your payment gateway
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {quickActions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <Button
                      key={action.title}
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-start gap-2 hover:border-blue-300"
                      onClick={() => navigate(action.href)}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <div className={`p-2 rounded-lg ${action.color}`}>
                          <Icon className="h-4 w-4 text-white" />
                        </div>
                        <div className="text-left">
                          <h3 className="font-semibold text-sm">{action.title}</h3>
                          <p className="text-xs text-gray-600">{action.description}</p>
                        </div>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Business Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Business Name:</span>
                    <p className="font-semibold">{merchant.business_name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Business Type:</span>
                    <p className="font-semibold">{merchant.business_type}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Integration Types:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {merchant.integration_types?.map((type) => (
                        <Badge key={type} variant="outline" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Settlement:</span>
                    <p className="font-semibold capitalize">{merchant.settlement_preference}</p>
                  </div>
                  {merchant.website_url && (
                    <div className="col-span-2">
                      <span className="font-medium text-gray-600">Website:</span>
                      <a 
                        href={merchant.website_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-semibold text-blue-600 hover:underline flex items-center gap-1"
                      >
                        {merchant.website_url}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
                <CardDescription>
                  Next steps to start accepting payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <span className="text-sm">Business registered</span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      merchant.verification_status === 'approved' 
                        ? 'bg-green-100' 
                        : 'bg-orange-100'
                    }`}>
                      {merchant.verification_status === 'approved' ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Clock className="h-4 w-4 text-orange-600" />
                      )}
                    </div>
                    <span className="text-sm">
                      {merchant.verification_status === 'approved' 
                        ? 'Account verified' 
                        : 'Account verification pending'}
                    </span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-blue-600">3</span>
                    </div>
                    <span className="text-sm">Get API keys and integrate</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-gray-600">4</span>
                    </div>
                    <span className="text-sm">Start accepting payments</span>
                  </div>

                  <div className="pt-4">
                    <Button 
                      onClick={() => navigate('/integration-guide')}
                      className="w-full"
                    >
                      <Code className="h-4 w-4 mr-2" />
                      View Integration Guide
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentGatewayDashboard;
