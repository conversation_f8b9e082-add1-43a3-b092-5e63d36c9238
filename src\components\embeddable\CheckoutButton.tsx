/**
 * Simple Checkout Button
 * 
 * A simple button that businesses can embed to trigger crypto payments
 * Opens payment in a modal or new window
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CreditCard,
  Zap,
  Shield,
  ExternalLink
} from 'lucide-react';
import PaymentWidget from './PaymentWidget';

interface CheckoutButtonProps {
  // Required props
  merchantId: string;
  amount: number;
  currency?: string;
  
  // Optional customization
  buttonText?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'secondary';
  theme?: 'light' | 'dark';
  primaryColor?: string;
  
  // Payment options
  acceptedCryptos?: string[];
  openInNewWindow?: boolean;
  
  // Callbacks
  onSuccess?: (paymentData: any) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

const CheckoutButton: React.FC<CheckoutButtonProps> = ({
  merchantId,
  amount,
  currency = 'USD',
  buttonText = 'Pay with Crypto',
  description = 'Secure cryptocurrency payment',
  size = 'md',
  variant = 'default',
  theme = 'light',
  primaryColor = '#3b82f6',
  acceptedCryptos = ['SOL', 'USDC', 'ETH'],
  openInNewWindow = false,
  onSuccess,
  onError,
  onCancel
}) => {
  const [showModal, setShowModal] = useState(false);

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const handleClick = () => {
    if (openInNewWindow) {
      // Create payment intent and open in new window
      createPaymentAndRedirect();
    } else {
      // Open modal
      setShowModal(true);
    }
  };

  const createPaymentAndRedirect = async () => {
    try {
      const response = await fetch('/api/v1/payment_intents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${merchantId}`
        },
        body: JSON.stringify({
          amount: amount * 100,
          currency: currency,
          accepted_cryptocurrencies: acceptedCryptos,
          metadata: {
            checkout_button: true,
            description: description
          }
        })
      });

      const paymentIntent = await response.json();

      if (response.ok) {
        window.open(paymentIntent.payment_url, '_blank');
        if (onSuccess) {
          onSuccess(paymentIntent);
        }
      } else {
        throw new Error(paymentIntent.error?.message || 'Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      if (onError) {
        onError(error instanceof Error ? error.message : 'Payment failed');
      }
    }
  };

  const handleModalSuccess = (paymentData: any) => {
    setShowModal(false);
    if (onSuccess) {
      onSuccess(paymentData);
    }
  };

  const handleModalError = (error: string) => {
    if (onError) {
      onError(error);
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    if (onCancel) {
      onCancel();
    }
  };

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4',
    lg: 'h-12 px-6 text-lg'
  };

  const buttonStyle = variant === 'default' ? {
    backgroundColor: primaryColor,
    borderColor: primaryColor,
    color: 'white'
  } : {};

  return (
    <>
      <Button
        onClick={handleClick}
        variant={variant}
        className={`${sizeClasses[size]} flex items-center gap-2 font-semibold`}
        style={buttonStyle}
      >
        <CreditCard className="h-4 w-4" />
        {buttonText}
        <Badge variant="secondary" className="ml-2 text-xs">
          {formatAmount(amount, currency)}
        </Badge>
        {openInNewWindow && <ExternalLink className="h-3 w-3 ml-1" />}
      </Button>

      {/* Modal for embedded checkout */}
      <Dialog open={showModal} onOpenChange={handleModalClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Crypto Payment
            </DialogTitle>
            <DialogDescription>
              {description}
            </DialogDescription>
          </DialogHeader>
          
          <PaymentWidget
            merchantId={merchantId}
            amount={amount}
            currency={currency}
            description={description}
            acceptedCryptos={acceptedCryptos}
            theme={theme}
            primaryColor={primaryColor}
            onSuccess={handleModalSuccess}
            onError={handleModalError}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

// Export a simple function for easy integration
export const createCheckoutButton = (config: CheckoutButtonProps) => {
  return <CheckoutButton {...config} />;
};

export default CheckoutButton;
