
import { Toaster } from "@/components/ui/toaster";
import { Toaster as SonnerToaster } from "sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, Outlet } from "react-router-dom";
import { useState, useEffect } from "react";
import Dashboard from "./pages/Dashboard";
import Services from "./pages/Services";
import NotFound from "./pages/NotFound";
import Settings from "./pages/Settings";
import VirtualCardPage from "./pages/VirtualCardPage";
import Profile from "./pages/Profile";
import Wallets from "./pages/Wallets";
import Transactions from "./pages/Transactions";
import CardRegistration from "./pages/CardRegistration";
import Auth from "./pages/Auth";
import Swap from "./pages/Swap";
import TransactionHistoryPage from "./pages/TransactionHistoryPage";
// Import Notifications directly with the full path
import Notifications from "./pages/Notifications.tsx";
import NotificationDetail from "./pages/NotificationDetail";
import TestPage from "./pages/TestPage";
import AdminDashboard from "./pages/AdminDashboard";
import AdminWaitlist from "./pages/AdminWaitlist";
import TestKYC from "./pages/TestKYC";
import CameraTest from "./pages/CameraTest";
import WaitlistSettings from "./pages/WaitlistSettings";
import MobileCameraTest from "./pages/MobileCameraTest";
import MobileDebug from "./pages/MobileDebug";
import EmergencyTest from "./pages/EmergencyTest";
import ClearCache from "./pages/ClearCache";
import CrossChainDashboard from "./pages/CrossChainDashboard";
import CrossChainTest from "./pages/CrossChainTest";
import { CacheManager } from "@/utils/cacheUtils";
import WaitlistDetails from "./pages/WaitlistDetails";
import WaitlistWelcomePage from "./pages/WaitlistWelcome";
import OffRampPage from "./pages/OffRamp";
import ProtectedRoute from "./components/ProtectedRoute";
import GroupCards from "./pages/group-cards";
import GroupCardDetail from "./pages/group-cards/[id]";

// Import new feature components
import MerchantRegistration from "./components/merchant/MerchantRegistration";
import MerchantDashboard from "./pages/MerchantDashboard";
import MerchantPayment from "./pages/MerchantPayment";
import MerchantPayments from "./pages/MerchantPayments";
import MerchantApiKeys from "./pages/MerchantApiKeys";
import MerchantPaymentIntents from "./pages/MerchantPaymentIntents";
import MerchantPaymentLinks from "./pages/MerchantPaymentLinks";
import PaymentPage from "./pages/PaymentPage";
import PaymentLinkPage from "./pages/PaymentLinkPage";
import PaymentGatewayRegistration from "./pages/PaymentGatewayRegistration";
import IntegrationGuide from "./pages/IntegrationGuide";
import Demo from "./pages/Demo";
import DemoPaymentPage from "./pages/DemoPaymentPage";
import PaymentGatewayDashboard from "./pages/PaymentGatewayDashboard";
import MerchantApiKeys from "./pages/MerchantApiKeys";
import CryptoPayment from "./pages/CryptoPayment";
import ExchangeRateTest from "./pages/ExchangeRateTest";
import QRScanner from "./components/qr/QRScanner";
import TelegramIntegration from "./components/telegram/TelegramIntegration";
import VoiceCommands from "./components/voice/VoiceCommands";
import Features from "./pages/Features";
import TelegramSetup from "./pages/admin/telegram-setup";

// Import context providers
import { WalletProvider } from "./contexts/WalletContext";
import { CardProvider } from "./contexts/CardContext";
import { SubscriptionProvider } from "./contexts/SubscriptionContext";
import { AuthProvider } from "./contexts/AuthContext";
import { AdminAuthProvider } from "./contexts/AdminAuthContext";
import { GroupCardProvider } from "./contexts/GroupCardContext";
import { RecurringPaymentProvider } from "./contexts/RecurringPaymentContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import SimpleVirtualCard from "./pages/SimpleVirtualCard";
import AdminLogin from "./pages/AdminLogin";
import AdminMainDashboard from "./pages/AdminMainDashboard";
import AdminTransfers from "./pages/AdminTransfers";
import AdminNIBSSStatus from "./pages/AdminNIBSSStatus";
import AdminUsers from "./pages/AdminUsers";
import AdminAnalytics from "./pages/AdminAnalytics";
import AdminSettings from "./pages/AdminSettings";
import AdminWaitlistManagement from "./pages/AdminWaitlistManagement";
import AdminUserManagement from "./pages/AdminUserManagement";
import AdminDebug from "./pages/AdminDebug";
import AdminMerchants from "./pages/AdminMerchants";
import AdminProtectedRoute from "./components/AdminProtectedRoute";

function App() {
  // Create a client inside the component
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 1
      }
    }
  }));
  const [isLoading, setIsLoading] = useState(true);

  // Add a brief delay to ensure all providers are properly initialized
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);

    // Initialize cache management for mobile PWA updates
    CacheManager.init();

    // Log version info for debugging
    console.log('SolPay Version Info:', {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    });

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="flex flex-col items-center space-y-6">
          {/* App Logo */}
          <div className="relative">
            <img
              src="/sola.png"
              alt="Solpay"
              className="w-24 h-24 animate-pulse"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-ping"></div>
          </div>

          {/* Loading Text */}
          <div className="text-center">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              Solpay
            </h2>
            <p className="text-gray-600 animate-pulse">Loading your crypto experience...</p>
          </div>

          {/* Loading Spinner */}
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <AdminAuthProvider>
            <NotificationProvider>
              <WalletProvider>
              <CardProvider>
                <SubscriptionProvider>
                  <RecurringPaymentProvider>
                    <GroupCardProvider>
                      <TooltipProvider>
                        <Toaster />
                        <SonnerToaster />
                        <Routes>
                          {/* Public routes */}
                          <Route path="/auth" element={<Auth />} />
                          <Route path="/test" element={<TestPage />} />
                          {/* Admin Routes */}
                          <Route path="/admin/login" element={<AdminLogin />} />
                          <Route path="/admin/debug" element={<AdminDebug />} />
                          <Route path="/admin/dashboard" element={
                            <AdminProtectedRoute requiredPermission="view_dashboard">
                              <AdminMainDashboard />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/transfers" element={
                            <AdminProtectedRoute requiredPermission="process_manual_transfers">
                              <AdminTransfers />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/nibss-status" element={
                            <AdminProtectedRoute requiredPermission="manage_nibss_status">
                              <AdminNIBSSStatus />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/users" element={
                            <AdminProtectedRoute requiredPermission="view_users">
                              <AdminUsers />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/analytics" element={
                            <AdminProtectedRoute requiredPermission="view_analytics">
                              <AdminAnalytics />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/settings" element={
                            <AdminProtectedRoute requiredPermission="manage_settings">
                              <AdminSettings />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/waitlist" element={
                            <AdminProtectedRoute requiredPermission="view_waitlist">
                              <AdminWaitlistManagement />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/admin-users" element={
                            <AdminProtectedRoute requiredPermission="manage_admins">
                              <AdminUserManagement />
                            </AdminProtectedRoute>
                          } />
                          <Route path="/admin/merchants" element={
                            <AdminProtectedRoute requiredPermission="manage_merchants">
                              <AdminMerchants />
                            </AdminProtectedRoute>
                          } />

                          {/* Legacy admin routes */}
                          <Route path="/admin" element={<AdminDashboard />} />
                          <Route path="/test-kyc" element={<TestKYC />} />
                          <Route path="/camera-test" element={<CameraTest />} />
                          <Route path="/waitlist-settings" element={<WaitlistSettings />} />
                          <Route path="/mobile-camera-test" element={<MobileCameraTest />} />
                          <Route path="/mobile-debug" element={<MobileDebug />} />
                          <Route path="/emergency" element={<EmergencyTest />} />
                          <Route path="/clear-cache" element={<ClearCache />} />
                          <Route path="/waitlist-details" element={<WaitlistDetails />} />
                          <Route path="/waitlist-welcome" element={<WaitlistWelcomePage />} />
                          <Route path="/off-ramp" element={<OffRampPage />} />

                          {/* Protected routes */}
                          <Route element={<ProtectedRoute />}>
                            <Route path="/" element={<Dashboard />} />
                            <Route path="/services" element={<Services />} />
                            <Route path="/settings" element={<Settings />} />
                            <Route path="/virtual-card" element={<VirtualCardPage />} />
                            <Route path="/simple-virtual-card" element={<SimpleVirtualCard />} />
                            <Route path="/card-registration" element={<CardRegistration />} />
                            <Route path="/profile" element={<Profile />} />
                            <Route path="/wallets" element={<Wallets />} />
                            <Route path="/transactions" element={<Transactions />} />
                            <Route path="/transaction-history" element={<TransactionHistoryPage />} />
                            <Route path="/swap" element={<Swap />} />
                            <Route path="/cross-chain" element={<CrossChainDashboard />} />
                            <Route path="/cross-chain-test" element={<CrossChainTest />} />
                            <Route path="/notifications" element={<Notifications />} />
                            <Route path="/notifications/:id" element={<NotificationDetail />} />
                            <Route path="/group-cards/*" element={
                              <Routes>
                                <Route index element={<GroupCards />} />
                                <Route path=":id" element={<GroupCardDetail />} />
                              </Routes>
                            } />

                            {/* New Feature Routes */}
                            <Route path="/features" element={<Features />} />
                            <Route path="/merchant-register" element={<MerchantRegistration />} />
              <Route path="/merchant-dashboard" element={<MerchantDashboard />} />
              <Route path="/merchant-payments" element={<MerchantPayments />} />
              <Route path="/merchant-api-keys" element={<MerchantApiKeys />} />
              <Route path="/merchant-payment-intents" element={<MerchantPaymentIntents />} />
              <Route path="/merchant-payment-links" element={<MerchantPaymentLinks />} />
              <Route path="/link/:url_slug" element={<PaymentLinkPage />} />
              <Route path="/payment-gateway-register" element={<PaymentGatewayRegistration />} />
              <Route path="/payment-gateway-dashboard" element={<PaymentGatewayDashboard />} />
              <Route path="/merchant-api-keys" element={<MerchantApiKeys />} />
              <Route path="/integration-guide" element={<IntegrationGuide />} />
              <Route path="/demo" element={<Demo />} />
              <Route path="/pay/:payment_intent_id" element={<PaymentPage />} />

              {/* Demo routes - handle demo payment intents */}
              <Route path="/pay/pi_demo_*" element={<DemoPaymentPage />} />
              <Route path="/pay/:merchantId" element={<MerchantPayment />} />
              <Route path="/crypto-payment" element={<CryptoPayment />} />
              <Route path="/exchange-rate-test" element={<ExchangeRateTest />} />
              <Route path="/cross-chain-test" element={<CrossChainTest />} />
                            <Route path="/qr-scanner" element={<QRScanner />} />
                            <Route path="/telegram" element={<TelegramIntegration />} />
                            <Route path="/voice-commands" element={<VoiceCommands />} />
                            <Route path="/admin/telegram-setup" element={<TelegramSetup />} />
                          </Route>

                          {/* Fallback route */}
                          <Route path="*" element={<NotFound />} />
                        </Routes>
                      </TooltipProvider>
                    </GroupCardProvider>
                  </RecurringPaymentProvider>
                </SubscriptionProvider>
              </CardProvider>
              </WalletProvider>
            </NotificationProvider>
          </AdminAuthProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
}

export default App;
