/**
 * Multi-Chain Payment Page
 * 
 * Enhanced payment page that supports multiple blockchains
 * Shows when customers click payment links from merchants
 */

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Wallet,
  CreditCard,
  Shield,
  CheckCircle,
  Clock,
  ExternalLink,
  Copy,
  QrCode,
  Smartphone,
  Globe,
  Zap,
  ArrowRight,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface PaymentIntent {
  id: string;
  merchant_id?: string;
  payment_gateway_merchant_id?: string;
  amount: number;
  currency: string;
  description?: string;
  title?: string;
  status: string;
  supported_chains?: string[];
  supported_tokens?: string[];
  payment_gateway_merchants?: {
    business_name: string;
    business_type: string;
    website_url?: string;
  };
}

interface ChainConfig {
  id: string;
  name: string;
  symbol: string;
  icon: string;
  color: string;
  rpcUrl: string;
  explorerUrl: string;
  tokens: TokenConfig[];
}

interface TokenConfig {
  symbol: string;
  name: string;
  address?: string;
  decimals: number;
  icon: string;
}

const SUPPORTED_CHAINS: ChainConfig[] = [
  {
    id: 'solana',
    name: 'Solana',
    symbol: 'SOL',
    icon: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
    color: 'bg-purple-500',
    rpcUrl: 'https://api.mainnet-beta.solana.com',
    explorerUrl: 'https://solscan.io',
    tokens: [
      { symbol: 'SOL', name: 'Solana', decimals: 9, icon: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png' },
      { symbol: 'USDC', name: 'USD Coin', address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', decimals: 6, icon: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png' },
      { symbol: 'USDT', name: 'Tether', address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', decimals: 6, icon: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB/logo.png' }
    ]
  },
  {
    id: 'ethereum',
    name: 'Ethereum',
    symbol: 'ETH',
    icon: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png',
    color: 'bg-blue-500',
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
    explorerUrl: 'https://etherscan.io',
    tokens: [
      { symbol: 'ETH', name: 'Ethereum', decimals: 18, icon: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png' },
      { symbol: 'USDC', name: 'USD Coin', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png' },
      { symbol: 'USDT', name: 'Tether', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png' }
    ]
  },
  {
    id: 'polygon',
    name: 'Polygon',
    symbol: 'MATIC',
    icon: 'https://assets.coingecko.com/coins/images/4713/small/matic-token-icon.png',
    color: 'bg-purple-600',
    rpcUrl: 'https://polygon-rpc.com',
    explorerUrl: 'https://polygonscan.com',
    tokens: [
      { symbol: 'MATIC', name: 'Polygon', decimals: 18, icon: 'https://assets.coingecko.com/coins/images/4713/small/matic-token-icon.png' },
      { symbol: 'USDC', name: 'USD Coin', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png' },
      { symbol: 'USDT', name: 'Tether', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png' }
    ]
  },
  {
    id: 'base',
    name: 'Base',
    symbol: 'ETH',
    icon: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png',
    color: 'bg-blue-600',
    rpcUrl: 'https://mainnet.base.org',
    explorerUrl: 'https://basescan.org',
    tokens: [
      { symbol: 'ETH', name: 'Ethereum', decimals: 18, icon: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png' },
      { symbol: 'USDC', name: 'USD Coin', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png' }
    ]
  },
  {
    id: 'avalanche',
    name: 'Avalanche',
    symbol: 'AVAX',
    icon: 'https://assets.coingecko.com/coins/images/12559/small/Avalanche_Circle_RedWhite_Trans.png',
    color: 'bg-red-500',
    rpcUrl: 'https://api.avax.network/ext/bc/C/rpc',
    explorerUrl: 'https://snowtrace.io',
    tokens: [
      { symbol: 'AVAX', name: 'Avalanche', decimals: 18, icon: 'https://assets.coingecko.com/coins/images/12559/small/Avalanche_Circle_RedWhite_Trans.png' },
      { symbol: 'USDC', name: 'USD Coin', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png' },
      { symbol: 'USDT', name: 'Tether', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png' }
    ]
  },
  {
    id: 'arbitrum',
    name: 'Arbitrum',
    symbol: 'ETH',
    icon: 'https://assets.coingecko.com/coins/images/16547/small/photo_2023-03-29_21.47.00.jpeg',
    color: 'bg-blue-400',
    rpcUrl: 'https://arb1.arbitrum.io/rpc',
    explorerUrl: 'https://arbiscan.io',
    tokens: [
      { symbol: 'ETH', name: 'Ethereum', decimals: 18, icon: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png' },
      { symbol: 'ARB', name: 'Arbitrum', decimals: 18, icon: 'https://assets.coingecko.com/coins/images/16547/small/photo_2023-03-29_21.47.00.jpeg' },
      { symbol: 'USDC', name: 'USD Coin', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png' },
      { symbol: 'USDT', name: 'Tether', address: '******************************************', decimals: 6, icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png' }
    ]
  }
];

const MultiChainPaymentPage: React.FC = () => {
  const { paymentId } = useParams<{ paymentId: string }>();
  const [searchParams] = useSearchParams();
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedChain, setSelectedChain] = useState<ChainConfig>(SUPPORTED_CHAINS[0]);
  const [selectedToken, setSelectedToken] = useState<TokenConfig>(SUPPORTED_CHAINS[0].tokens[0]);
  const [walletConnected, setWalletConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'processing' | 'completed' | 'failed'>('pending');
  const { toast } = useToast();

  useEffect(() => {
    if (paymentId) {
      loadPaymentIntent();
    }
  }, [paymentId]);

  const loadPaymentIntent = async () => {
    try {
      const { data: payment, error } = await supabase
        .from('payment_intents')
        .select(`
          *,
          payment_gateway_merchants!inner(
            business_name,
            business_type,
            website_url
          )
        `)
        .eq('id', paymentId)
        .single();

      if (error || !payment) {
        toast({
          title: "Payment Not Found",
          description: "The payment link you're looking for doesn't exist or has expired.",
          variant: "destructive",
        });
        return;
      }

      setPaymentIntent(payment);

    } catch (error) {
      console.error('Error loading payment intent:', error);
      toast({
        title: "Error",
        description: "Failed to load payment details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const connectWallet = async (chainId: string) => {
    try {
      if (chainId === 'solana') {
        // Connect Solana wallet (Phantom, Solflare, etc.)
        if ('solana' in window) {
          const provider = (window as any).solana;
          if (provider.isPhantom) {
            const response = await provider.connect();
            setWalletAddress(response.publicKey.toString());
            setWalletConnected(true);
            toast({
              title: "Wallet Connected! 🎉",
              description: "Your Solana wallet has been connected successfully.",
            });
          }
        } else {
          toast({
            title: "Wallet Not Found",
            description: "Please install a Solana wallet like Phantom or Solflare.",
            variant: "destructive",
          });
        }
      } else if (chainId === 'ethereum' || chainId === 'polygon') {
        // Connect Ethereum/Polygon wallet (MetaMask, etc.)
        if ('ethereum' in window) {
          const provider = (window as any).ethereum;
          const accounts = await provider.request({ method: 'eth_requestAccounts' });
          setWalletAddress(accounts[0]);
          setWalletConnected(true);
          toast({
            title: "Wallet Connected! 🎉",
            description: `Your ${selectedChain.name} wallet has been connected successfully.`,
          });
        } else {
          toast({
            title: "Wallet Not Found",
            description: "Please install MetaMask or another Ethereum wallet.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error('Error connecting wallet:', error);
      toast({
        title: "Connection Failed",
        description: "Failed to connect wallet. Please try again.",
        variant: "destructive",
      });
    }
  };

  const processPayment = async () => {
    if (!paymentIntent || !walletConnected) return;

    setPaymentStatus('processing');
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update payment status in database
      const { error } = await supabase
        .from('payment_intents')
        .update({ 
          status: 'completed',
          paid_at: new Date().toISOString(),
          payment_method: `${selectedChain.name} - ${selectedToken.symbol}`
        })
        .eq('id', paymentIntent.id);

      if (error) throw error;

      setPaymentStatus('completed');
      toast({
        title: "Payment Successful! 🎉",
        description: "Your payment has been processed successfully.",
      });

    } catch (error) {
      console.error('Error processing payment:', error);
      setPaymentStatus('failed');
      toast({
        title: "Payment Failed",
        description: "Failed to process payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  if (!paymentIntent) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Not Found</h2>
            <p className="text-gray-600">
              The payment link you're looking for doesn't exist or has expired.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <img
                src="/solana.png"
                alt="SOLPAY Logo"
                className="h-16 w-16"
              />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              SOLPAY
            </h1>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Complete Your Payment
            </h2>
            <p className="text-gray-600">
              Secure multi-chain cryptocurrency payment
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Payment Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Merchant:</span>
                  <span className="font-semibold">{paymentIntent.payment_gateway_merchants?.business_name || 'Unknown Merchant'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-semibold text-lg">
                    {formatAmount(paymentIntent.amount, paymentIntent.currency)}
                  </span>
                </div>
                {(paymentIntent.description || paymentIntent.title) && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Description:</span>
                    <span className="font-semibold">{paymentIntent.title || paymentIntent.description}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge variant={paymentStatus === 'completed' ? 'default' : 'outline'}>
                    {paymentStatus}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Payment Method Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5" />
                  Choose Payment Method
                </CardTitle>
                <CardDescription>
                  Select your preferred blockchain and token
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={selectedChain.id} onValueChange={(value) => {
                  const chain = SUPPORTED_CHAINS.find(c => c.id === value);
                  if (chain) {
                    setSelectedChain(chain);
                    setSelectedToken(chain.tokens[0]);
                    setWalletConnected(false);
                    setWalletAddress('');
                  }
                }}>
                  <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-1">
                    {SUPPORTED_CHAINS.map((chain) => (
                      <TabsTrigger key={chain.id} value={chain.id} className="flex items-center gap-1 text-xs lg:text-sm">
                        <img src={chain.icon} alt={chain.name} className="h-4 w-4" />
                        <span className="hidden sm:inline">{chain.name}</span>
                        <span className="sm:hidden">{chain.symbol}</span>
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {SUPPORTED_CHAINS.map((chain) => (
                    <TabsContent key={chain.id} value={chain.id} className="space-y-4">
                      {/* Token Selection */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Select Token</Label>
                        <div className="grid grid-cols-1 gap-2">
                          {chain.tokens.map((token) => (
                            <Button
                              key={token.symbol}
                              variant={selectedToken.symbol === token.symbol ? "default" : "outline"}
                              onClick={() => setSelectedToken(token)}
                              className="justify-start"
                            >
                              <img src={token.icon} alt={token.symbol} className="h-5 w-5 mr-2" />
                              {token.symbol} - {token.name}
                            </Button>
                          ))}
                        </div>
                      </div>

                      {/* Wallet Connection */}
                      <div className="space-y-3">
                        {!walletConnected ? (
                          <Button 
                            onClick={() => connectWallet(chain.id)}
                            className="w-full"
                            size="lg"
                          >
                            <Wallet className="h-5 w-5 mr-2" />
                            Connect {chain.name} Wallet
                          </Button>
                        ) : (
                          <div className="space-y-3">
                            <Alert>
                              <CheckCircle className="h-4 w-4" />
                              <AlertDescription>
                                Wallet connected: {walletAddress.slice(0, 8)}...{walletAddress.slice(-8)}
                              </AlertDescription>
                            </Alert>
                            
                            <Button 
                              onClick={processPayment}
                              disabled={paymentStatus === 'processing' || paymentStatus === 'completed'}
                              className="w-full"
                              size="lg"
                            >
                              {paymentStatus === 'processing' ? (
                                <>
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                                  Processing Payment...
                                </>
                              ) : paymentStatus === 'completed' ? (
                                <>
                                  <CheckCircle className="h-5 w-5 mr-2" />
                                  Payment Completed
                                </>
                              ) : (
                                <>
                                  <ArrowRight className="h-5 w-5 mr-2" />
                                  Pay {formatAmount(paymentIntent.amount, paymentIntent.currency)}
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <div className="flex items-center justify-center gap-2 mb-2">
              <img
                src="/solana.png"
                alt="SOLPAY Logo"
                className="h-4 w-4"
              />
              <p>Powered by <span className="font-semibold text-blue-600">SOLPAY</span></p>
            </div>
            <p>Secure multi-chain payment gateway</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiChainPaymentPage;
