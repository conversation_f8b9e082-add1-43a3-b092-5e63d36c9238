/**
 * Embeddable Payment Widget
 * 
 * This component can be embedded on merchant websites
 * to accept crypto payments directly
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  CreditCard,
  Shield,
  Zap,
  CheckCircle,
  Clock,
  ArrowRight,
  Copy,
  ExternalLink
} from 'lucide-react';

interface PaymentWidgetProps {
  // Configuration props that merchants would set
  merchantId: string;
  amount?: number;
  currency?: string;
  description?: string;
  allowCustomAmount?: boolean;
  minAmount?: number;
  maxAmount?: number;
  acceptedCryptos?: string[];
  theme?: 'light' | 'dark';
  primaryColor?: string;
  onSuccess?: (paymentData: any) => void;
  onError?: (error: string) => void;
}

const PaymentWidget: React.FC<PaymentWidgetProps> = ({
  merchantId,
  amount,
  currency = 'USD',
  description = 'Payment',
  allowCustomAmount = false,
  minAmount = 1,
  maxAmount = 10000,
  acceptedCryptos = ['SOL', 'USDC', 'ETH'],
  theme = 'light',
  primaryColor = '#3b82f6',
  onSuccess,
  onError
}) => {
  const [currentStep, setCurrentStep] = useState<'amount' | 'crypto' | 'processing' | 'success'>('amount');
  const [customAmount, setCustomAmount] = useState(amount || minAmount);
  const [selectedCrypto, setSelectedCrypto] = useState('');
  const [paymentData, setPaymentData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const getCryptoIcon = (crypto: string) => {
    const icons = {
      'SOL': '◎',
      'USDC': '💵',
      'ETH': 'Ξ',
      'BTC': '₿'
    };
    return icons[crypto as keyof typeof icons] || '🪙';
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const handleAmountSubmit = () => {
    if (allowCustomAmount && (customAmount < minAmount || customAmount > maxAmount)) {
      toast({
        title: "Invalid Amount",
        description: `Amount must be between ${formatAmount(minAmount, currency)} and ${formatAmount(maxAmount, currency)}`,
        variant: "destructive",
      });
      return;
    }
    setCurrentStep('crypto');
  };

  const handleCryptoSelection = async (crypto: string) => {
    setSelectedCrypto(crypto);
    setCurrentStep('processing');
    setLoading(true);

    try {
      // Create payment intent via API
      const response = await fetch('/api/v1/payment_intents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${merchantId}` // This would be the merchant's API key
        },
        body: JSON.stringify({
          amount: (allowCustomAmount ? customAmount : amount!) * 100, // Convert to cents
          currency: currency,
          accepted_cryptocurrencies: [crypto],
          metadata: {
            widget_payment: true,
            description: description
          }
        })
      });

      const paymentIntent = await response.json();

      if (response.ok) {
        setPaymentData(paymentIntent);
        setCurrentStep('success');
        
        if (onSuccess) {
          onSuccess(paymentIntent);
        }
      } else {
        throw new Error(paymentIntent.error?.message || 'Payment failed');
      }

    } catch (error) {
      console.error('Payment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      
      toast({
        title: "Payment Failed",
        description: errorMessage,
        variant: "destructive",
      });

      if (onError) {
        onError(errorMessage);
      }

      setCurrentStep('crypto');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Payment link copied to clipboard",
    });
  };

  const themeClasses = theme === 'dark' 
    ? 'bg-gray-900 text-white border-gray-700' 
    : 'bg-white text-gray-900 border-gray-200';

  const buttonStyle = {
    backgroundColor: primaryColor,
    borderColor: primaryColor
  };

  return (
    <Card className={`w-full max-w-md mx-auto shadow-lg ${themeClasses}`}>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <CreditCard className="h-5 w-5" />
          Pay with Crypto
        </CardTitle>
        <CardDescription className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>
          {description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {currentStep === 'amount' && (
          <div className="space-y-4">
            {allowCustomAmount ? (
              <div>
                <Label htmlFor="amount">Amount ({currency})</Label>
                <Input
                  id="amount"
                  type="number"
                  value={customAmount}
                  onChange={(e) => setCustomAmount(parseFloat(e.target.value) || 0)}
                  min={minAmount}
                  max={maxAmount}
                  step="0.01"
                  className="text-lg font-semibold text-center"
                />
                <p className="text-xs text-gray-500 text-center mt-1">
                  Min: {formatAmount(minAmount, currency)} • Max: {formatAmount(maxAmount, currency)}
                </p>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  {formatAmount(amount!, currency)}
                </div>
                <p className="text-sm text-gray-600">Fixed amount</p>
              </div>
            )}

            <Button 
              onClick={handleAmountSubmit}
              className="w-full"
              style={buttonStyle}
            >
              Continue to Payment
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>

            <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                <span>Secure</span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                <span>Fast</span>
              </div>
            </div>
          </div>
        )}

        {currentStep === 'crypto' && (
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="font-semibold mb-1">Choose Payment Method</h3>
              <p className="text-sm text-gray-600">
                Amount: {formatAmount(allowCustomAmount ? customAmount : amount!, currency)}
              </p>
            </div>

            <div className="grid grid-cols-1 gap-3">
              {acceptedCryptos.map((crypto) => (
                <Button
                  key={crypto}
                  variant="outline"
                  className="h-16 flex items-center justify-center gap-3 hover:border-blue-300"
                  onClick={() => handleCryptoSelection(crypto)}
                >
                  <span className="text-2xl">{getCryptoIcon(crypto)}</span>
                  <span className="font-semibold">{crypto}</span>
                </Button>
              ))}
            </div>

            <Button
              variant="ghost"
              onClick={() => setCurrentStep('amount')}
              className="w-full"
            >
              ← Back to Amount
            </Button>
          </div>
        )}

        {currentStep === 'processing' && (
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-blue-600 animate-spin" />
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-1">Creating Payment</h3>
              <p className="text-sm text-gray-600">
                Please wait while we prepare your crypto payment...
              </p>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg text-sm">
              <div><strong>Amount:</strong> {formatAmount(allowCustomAmount ? customAmount : amount!, currency)}</div>
              <div><strong>Method:</strong> {selectedCrypto}</div>
            </div>
          </div>
        )}

        {currentStep === 'success' && paymentData && (
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-1">Payment Created!</h3>
              <p className="text-sm text-gray-600">
                Complete your payment using the link below
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Input
                  value={paymentData.payment_url}
                  readOnly
                  className="text-xs"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(paymentData.payment_url)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>

              <Button
                onClick={() => window.open(paymentData.payment_url, '_blank')}
                className="w-full"
                style={buttonStyle}
              >
                Complete Payment
                <ExternalLink className="h-4 w-4 ml-2" />
              </Button>
            </div>

            <div className="bg-green-50 p-3 rounded-lg text-sm">
              <div><strong>Payment ID:</strong> {paymentData.id}</div>
              <div><strong>Amount:</strong> {formatAmount(paymentData.amount / 100, paymentData.currency)}</div>
            </div>
          </div>
        )}

        {/* Powered by footer */}
        <div className="text-center pt-4 border-t">
          <p className="text-xs text-gray-500">
            Powered by <span className="font-semibold">Your Crypto Gateway</span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentWidget;
