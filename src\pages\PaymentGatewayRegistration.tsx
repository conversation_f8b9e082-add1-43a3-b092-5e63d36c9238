/**
 * Payment Gateway Merchant Registration
 * 
 * Separate registration flow for businesses wanting to integrate
 * crypto payments via API (different from restaurant QR merchants)
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  Building2,
  Globe,
  CreditCard,
  Shield,
  Zap,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Code,
  Smartphone,
  ShoppingCart,
  Users,
  Briefcase,
  Monitor,
  DollarSign
} from 'lucide-react';

const PaymentGatewayRegistration: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Form data
  const [formData, setFormData] = useState({
    // Business Information
    business_name: '',
    business_type: '',
    business_category: '',
    website_url: '',
    business_description: '',
    
    // Contact Information
    contact_name: '',
    contact_email: '',
    contact_phone: '',
    business_address: '',
    country: 'Nigeria',
    
    // Business Details
    monthly_volume: '',
    integration_types: [], // Allow multiple integration types
    settlement_preference: 'mixed', // 'crypto', 'fiat', 'mixed'
    supported_currencies: ['NGN'],
    
    // Technical Information
    technical_contact_email: '',
    webhook_url: '',
    has_technical_team: false,
    
    // Compliance
    business_registration_number: '',
    tax_id: '',
    accepts_terms: false,
    accepts_kyb: false
  });

  const businessTypes = [
    { id: 'ecommerce', label: 'E-commerce Store', icon: ShoppingCart, description: 'Online retail, marketplace, dropshipping' },
    { id: 'saas', label: 'SaaS/Software', icon: Monitor, description: 'Software subscriptions, digital services' },
    { id: 'freelance', label: 'Freelancer/Agency', icon: Users, description: 'Consulting, design, development services' },
    { id: 'content', label: 'Content Creator', icon: Smartphone, description: 'Courses, digital products, memberships' },
    { id: 'nonprofit', label: 'Non-Profit', icon: Building2, description: 'Donations, fundraising, charity' },
    { id: 'other', label: 'Other Business', icon: Briefcase, description: 'Other business types' }
  ];

  const integrationTypes = [
    { id: 'api', label: 'API Integration', description: 'Full API integration with your platform' },
    { id: 'hosted', label: 'Hosted Checkout', description: 'Redirect customers to our secure checkout' },
    { id: 'links', label: 'Payment Links', description: 'Share payment links via email, social media' },
    { id: 'widgets', label: 'Embeddable Widgets', description: 'Embed payment forms on your website' }
  ];

  const monthlyVolumes = [
    { id: 'under_1k', label: 'Under $1,000', description: 'Just getting started' },
    { id: '1k_10k', label: '$1,000 - $10,000', description: 'Small business' },
    { id: '10k_50k', label: '$10,000 - $50,000', description: 'Growing business' },
    { id: '50k_250k', label: '$50,000 - $250,000', description: 'Established business' },
    { id: 'over_250k', label: 'Over $250,000', description: 'Enterprise' }
  ];

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to register as a payment gateway merchant",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    if (!formData.accepts_terms || !formData.accepts_kyb) {
      toast({
        title: "Terms Required",
        description: "Please accept the terms and conditions to continue",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Create payment gateway merchant account
      const { data, error } = await supabase
        .from('payment_gateway_merchants')
        .insert({
          user_id: user.id,
          business_name: formData.business_name,
          business_type: formData.business_type,
          business_category: formData.business_category,
          website_url: formData.website_url,
          business_description: formData.business_description,
          contact_name: formData.contact_name,
          contact_email: formData.contact_email,
          contact_phone: formData.contact_phone,
          business_address: formData.business_address,
          country: formData.country,
          monthly_volume: formData.monthly_volume,
          integration_types: formData.integration_types,
          settlement_preference: formData.settlement_preference,
          supported_currencies: formData.supported_currencies,
          technical_contact_email: formData.technical_contact_email,
          webhook_url: formData.webhook_url,
          has_technical_team: formData.has_technical_team,
          business_registration_number: formData.business_registration_number,
          tax_id: formData.tax_id,
          verification_status: 'pending',
          is_active: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating payment gateway merchant:', error);
        toast({
          title: "Registration Failed",
          description: "Failed to register your business. Please try again.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Registration Successful! 🎉",
        description: "Your payment gateway merchant account has been created. We'll review your application and get back to you within 24 hours.",
      });

      // Redirect to payment gateway dashboard
      navigate('/payment-gateway-dashboard');

    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Tell us about your business</h2>
        <p className="text-gray-600">Help us understand what type of business you run</p>
      </div>

      <div>
        <Label htmlFor="business_name">Business Name *</Label>
        <Input
          id="business_name"
          value={formData.business_name}
          onChange={(e) => setFormData(prev => ({ ...prev, business_name: e.target.value }))}
          placeholder="Your Business Name"
          className="mt-1"
        />
      </div>

      <div>
        <Label>Business Type *</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          {businessTypes.map((type) => {
            const Icon = type.icon;
            return (
              <Card
                key={type.id}
                className={`cursor-pointer transition-all ${
                  formData.business_type === type.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'hover:border-gray-300'
                }`}
                onClick={() => setFormData(prev => ({ ...prev, business_type: type.id }))}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Icon className="h-6 w-6 text-blue-600 mt-1" />
                    <div>
                      <h3 className="font-semibold text-sm">{type.label}</h3>
                      <p className="text-xs text-gray-600 mt-1">{type.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="website_url">Website URL</Label>
          <Input
            id="website_url"
            type="url"
            value={formData.website_url}
            onChange={(e) => setFormData(prev => ({ ...prev, website_url: e.target.value }))}
            placeholder="https://yourbusiness.com"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="monthly_volume">Expected Monthly Volume *</Label>
          <Select value={formData.monthly_volume} onValueChange={(value) => setFormData(prev => ({ ...prev, monthly_volume: value }))}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select volume range" />
            </SelectTrigger>
            <SelectContent>
              {monthlyVolumes.map((volume) => (
                <SelectItem key={volume.id} value={volume.id}>
                  <div>
                    <div className="font-medium">{volume.label}</div>
                    <div className="text-xs text-gray-500">{volume.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="business_description">Business Description</Label>
        <Textarea
          id="business_description"
          value={formData.business_description}
          onChange={(e) => setFormData(prev => ({ ...prev, business_description: e.target.value }))}
          placeholder="Briefly describe what your business does..."
          className="mt-1"
          rows={3}
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Contact Information</h2>
        <p className="text-gray-600">We need this information for verification and support</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="contact_name">Contact Name *</Label>
          <Input
            id="contact_name"
            value={formData.contact_name}
            onChange={(e) => setFormData(prev => ({ ...prev, contact_name: e.target.value }))}
            placeholder="John Doe"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="contact_email">Contact Email *</Label>
          <Input
            id="contact_email"
            type="email"
            value={formData.contact_email}
            onChange={(e) => setFormData(prev => ({ ...prev, contact_email: e.target.value }))}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="contact_phone">Phone Number</Label>
          <Input
            id="contact_phone"
            type="tel"
            value={formData.contact_phone}
            onChange={(e) => setFormData(prev => ({ ...prev, contact_phone: e.target.value }))}
            placeholder="+234 xxx xxx xxxx"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="country">Country *</Label>
          <Select value={formData.country} onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}>
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Nigeria">Nigeria</SelectItem>
              <SelectItem value="Ghana">Ghana</SelectItem>
              <SelectItem value="Kenya">Kenya</SelectItem>
              <SelectItem value="South Africa">South Africa</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="business_address">Business Address</Label>
        <Textarea
          id="business_address"
          value={formData.business_address}
          onChange={(e) => setFormData(prev => ({ ...prev, business_address: e.target.value }))}
          placeholder="Your business address..."
          className="mt-1"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="business_registration_number">Business Registration Number</Label>
          <Input
            id="business_registration_number"
            value={formData.business_registration_number}
            onChange={(e) => setFormData(prev => ({ ...prev, business_registration_number: e.target.value }))}
            placeholder="RC123456 or CAC number"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="tax_id">Tax ID (Optional)</Label>
          <Input
            id="tax_id"
            value={formData.tax_id}
            onChange={(e) => setFormData(prev => ({ ...prev, tax_id: e.target.value }))}
            placeholder="Tax identification number"
            className="mt-1"
          />
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Integration Preferences</h2>
        <p className="text-gray-600">How do you want to integrate crypto payments?</p>
      </div>

      <div>
        <Label>Integration Types * (Select all that apply)</Label>
        <div className="grid grid-cols-1 gap-3 mt-2">
          {integrationTypes.map((type) => (
            <Card
              key={type.id}
              className={`cursor-pointer transition-all ${
                formData.integration_types.includes(type.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'hover:border-gray-300'
              }`}
              onClick={() => {
                const isSelected = formData.integration_types.includes(type.id);
                if (isSelected) {
                  setFormData(prev => ({
                    ...prev,
                    integration_types: prev.integration_types.filter(t => t !== type.id)
                  }));
                } else {
                  setFormData(prev => ({
                    ...prev,
                    integration_types: [...prev.integration_types, type.id]
                  }));
                }
              }}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{type.label}</h3>
                    <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                  </div>
                  <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                    formData.integration_types.includes(type.id)
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {formData.integration_types.includes(type.id) && (
                      <CheckCircle className="h-3 w-3 text-white" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <p className="text-xs text-gray-500 mt-2">
          You can select multiple integration methods. We'll help you implement them all.
        </p>
      </div>

      <div>
        <Label>Settlement Preference *</Label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2">
          <Card
            className={`cursor-pointer transition-all ${
              formData.settlement_preference === 'fiat'
                ? 'border-green-500 bg-green-50'
                : 'hover:border-gray-300'
            }`}
            onClick={() => setFormData(prev => ({ ...prev, settlement_preference: 'fiat' }))}
          >
            <CardContent className="p-4 text-center">
              <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold">Fiat Only</h3>
              <p className="text-xs text-gray-600 mt-1">Convert all crypto to NGN</p>
            </CardContent>
          </Card>

          <Card
            className={`cursor-pointer transition-all ${
              formData.settlement_preference === 'crypto'
                ? 'border-blue-500 bg-blue-50'
                : 'hover:border-gray-300'
            }`}
            onClick={() => setFormData(prev => ({ ...prev, settlement_preference: 'crypto' }))}
          >
            <CardContent className="p-4 text-center">
              <CreditCard className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold">Crypto Only</h3>
              <p className="text-xs text-gray-600 mt-1">Keep all payments in crypto</p>
            </CardContent>
          </Card>

          <Card
            className={`cursor-pointer transition-all ${
              formData.settlement_preference === 'mixed'
                ? 'border-purple-500 bg-purple-50'
                : 'hover:border-gray-300'
            }`}
            onClick={() => setFormData(prev => ({ ...prev, settlement_preference: 'mixed' }))}
          >
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold">Mixed</h3>
              <p className="text-xs text-gray-600 mt-1">Choose per transaction</p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="technical_contact_email">Technical Contact Email</Label>
          <Input
            id="technical_contact_email"
            type="email"
            value={formData.technical_contact_email}
            onChange={(e) => setFormData(prev => ({ ...prev, technical_contact_email: e.target.value }))}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="webhook_url">Webhook URL (Optional)</Label>
          <Input
            id="webhook_url"
            type="url"
            value={formData.webhook_url}
            onChange={(e) => setFormData(prev => ({ ...prev, webhook_url: e.target.value }))}
            placeholder="https://yourbusiness.com/webhooks"
            className="mt-1"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="has_technical_team"
          checked={formData.has_technical_team}
          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, has_technical_team: checked as boolean }))}
        />
        <Label htmlFor="has_technical_team" className="text-sm">
          We have a technical team that can handle API integration
        </Label>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review & Submit</h2>
        <p className="text-gray-600">Please review your information and accept our terms</p>
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Application Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Business:</span> {formData.business_name}
            </div>
            <div>
              <span className="font-medium">Type:</span> {businessTypes.find(t => t.id === formData.business_type)?.label}
            </div>
            <div>
              <span className="font-medium">Contact:</span> {formData.contact_name}
            </div>
            <div>
              <span className="font-medium">Email:</span> {formData.contact_email}
            </div>
            <div>
              <span className="font-medium">Integration:</span> {formData.integration_types.map(id => integrationTypes.find(t => t.id === id)?.label).join(', ')}
            </div>
            <div>
              <span className="font-medium">Settlement:</span> {formData.settlement_preference}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Terms */}
      <div className="space-y-4">
        <div className="flex items-start space-x-2">
          <Checkbox
            id="accepts_terms"
            checked={formData.accepts_terms}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, accepts_terms: checked as boolean }))}
          />
          <Label htmlFor="accepts_terms" className="text-sm">
            I accept the <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a> and <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>
          </Label>
        </div>

        <div className="flex items-start space-x-2">
          <Checkbox
            id="accepts_kyb"
            checked={formData.accepts_kyb}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, accepts_kyb: checked as boolean }))}
          />
          <Label htmlFor="accepts_kyb" className="text-sm">
            I understand that my business will undergo Know Your Business (KYB) verification
          </Label>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-2">What happens next?</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• We'll review your application within 24 hours</li>
          <li>• You'll receive API keys once approved</li>
          <li>• Our team will help you with integration</li>
          <li>• Start accepting crypto payments immediately</li>
        </ul>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Join Our Payment Gateway
            </h1>
            <p className="text-gray-600">
              Start accepting cryptocurrency payments on your website or app
            </p>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step < currentStep ? <CheckCircle className="h-5 w-5" /> : step}
                </div>
                {step < 4 && (
                  <div className={`w-16 h-1 mx-2 ${
                    step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* Form */}
          <Card className="shadow-xl">
            <CardContent className="p-8">
              {currentStep === 1 && renderStep1()}
              {currentStep === 2 && renderStep2()}
              {currentStep === 3 && renderStep3()}
              {currentStep === 4 && renderStep4()}

              {/* Navigation */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>

                {currentStep < 4 ? (
                  <Button
                    onClick={handleNext}
                    disabled={
                      (currentStep === 1 && (!formData.business_name || !formData.business_type || !formData.monthly_volume)) ||
                      (currentStep === 2 && (!formData.contact_name || !formData.contact_email)) ||
                      (currentStep === 3 && formData.integration_types.length === 0)
                    }
                    className="flex items-center gap-2"
                  >
                    Next
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={loading || !formData.accepts_terms || !formData.accepts_kyb}
                    className="flex items-center gap-2"
                  >
                    {loading ? 'Submitting...' : 'Submit Application'}
                    <CheckCircle className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <Card>
              <CardContent className="p-6 text-center">
                <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Secure & Compliant</h3>
                <p className="text-sm text-gray-600">
                  Bank-grade security with full regulatory compliance
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <Zap className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Fast Integration</h3>
                <p className="text-sm text-gray-600">
                  Get started in minutes with our simple API
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <Globe className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Global Reach</h3>
                <p className="text-sm text-gray-600">
                  Accept payments from customers worldwide
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentGatewayRegistration;
