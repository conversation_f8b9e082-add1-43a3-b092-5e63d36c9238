/**
 * Payment Gateway Payment Links
 * 
 * Page for payment gateway merchants to create and manage payment links
 * that support multiple blockchains and tokens
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  Link,
  ArrowLeft,
  Plus,
  Copy,
  ExternalLink,
  QrCode,
  Share,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

interface PaymentLink {
  id: string;
  title: string;
  description: string;
  amount: number;
  currency: string;
  supported_chains: string[];
  supported_tokens: string[];
  link_url: string;
  status: string;
  created_at: string;
  expires_at?: string;
}

const PaymentGatewayPaymentLinks: React.FC = () => {
  const [paymentLinks, setPaymentLinks] = useState<PaymentLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [merchantId, setMerchantId] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    currency: 'USD',
    supported_chains: ['solana'],
    supported_tokens: ['SOL', 'USDC-SOL'],
    expires_in_days: '30'
  });

  useEffect(() => {
    if (user) {
      loadMerchantAndLinks();
    }
  }, [user]);

  const loadMerchantAndLinks = async () => {
    try {
      // Get merchant ID
      const { data: merchantData, error: merchantError } = await supabase
        .from('payment_gateway_merchants')
        .select('id, business_name, verification_status')
        .eq('user_id', user?.id)
        .single();

      if (merchantError) {
        console.error('Error fetching merchant:', merchantError);
        toast({
          title: "Access Denied",
          description: "Please complete your payment gateway registration first",
          variant: "destructive",
        });
        navigate('/payment-gateway-register');
        return;
      }

      setMerchantId(merchantData.id);

      // Load payment links
      const { data: linksData, error: linksError } = await supabase
        .from('payment_intents')
        .select('*')
        .eq('payment_gateway_merchant_id', merchantData.id)
        .eq('type', 'payment_link')
        .order('created_at', { ascending: false });

      if (linksError) {
        console.error('Error fetching payment links:', linksError);
      } else {
        setPaymentLinks(linksData || []);
      }

    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "Failed to load payment links",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createPaymentLink = async () => {
    if (!merchantId || !formData.title.trim() || !formData.amount) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + parseInt(formData.expires_in_days));

      const paymentIntentData = {
        payment_gateway_merchant_id: merchantId,
        type: 'payment_link',
        title: formData.title.trim(),
        description: formData.description.trim(),
        amount: Math.round(parseFloat(formData.amount) * 100), // Convert to cents
        currency: formData.currency,
        supported_chains: formData.supported_chains,
        supported_tokens: formData.supported_tokens,
        status: 'active',
        expires_at: expiresAt.toISOString(),
        metadata: {
          created_via: 'payment_gateway_dashboard',
          expires_in_days: formData.expires_in_days
        }
      };

      const { data: paymentIntent, error } = await supabase
        .from('payment_intents')
        .insert(paymentIntentData)
        .select()
        .single();

      if (error) {
        console.error('Error creating payment link:', error);
        toast({
          title: "Creation Failed",
          description: "Failed to create payment link. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Generate the payment link URL
      const linkUrl = `${window.location.origin}/multichain-pay/${paymentIntent.id}`;

      // Update the payment intent with the link URL
      await supabase
        .from('payment_intents')
        .update({ link_url: linkUrl })
        .eq('id', paymentIntent.id);

      toast({
        title: "Payment Link Created! 🎉",
        description: "Your multi-chain payment link has been generated successfully.",
      });

      // Reset form and reload links
      setFormData({
        title: '',
        description: '',
        amount: '',
        currency: 'USD',
        supported_chains: ['solana'],
        supported_tokens: ['SOL', 'USDC-SOL'],
        expires_in_days: '30'
      });
      setShowCreateForm(false);
      await loadMerchantAndLinks();

    } catch (error) {
      console.error('Error creating payment link:', error);
      toast({
        title: "Error",
        description: "Failed to create payment link",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const copyLinkToClipboard = (url: string) => {
    navigator.clipboard.writeText(url);
    toast({
      title: "Copied! 📋",
      description: "Payment link copied to clipboard",
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment links...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" onClick={() => navigate('/payment-gateway-dashboard')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
            <div className="text-center mb-6">
              <div className="flex justify-center mb-4">
                <img
                  src="/solana.png"
                  alt="SOLPAY Logo"
                  className="h-16 w-16"
                />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                SOLPAY Payment Links
              </h1>
              <p className="text-gray-600 mb-6">
                Create shareable multi-chain payment links for your customers
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Payment Link
              </Button>
            </div>
          </div>

          {/* Create Payment Link Form */}
          {showCreateForm && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Create New Payment Link</CardTitle>
                <CardDescription>
                  Generate a shareable link that accepts payments across multiple blockchains
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="e.g., Product Purchase, Service Payment"
                    />
                  </div>
                  <div>
                    <Label htmlFor="amount">Amount *</Label>
                    <div className="flex">
                      <select
                        value={formData.currency}
                        onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                        className="px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50"
                      >
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        value={formData.amount}
                        onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                        placeholder="0.00"
                        className="rounded-l-none"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Optional description for the payment"
                    rows={3}
                  />
                </div>

                <div>
                  <Label>Supported Blockchains</Label>
                  <div className="flex flex-wrap gap-3 mt-2">
                    {['solana', 'ethereum', 'polygon'].map((chain) => (
                      <div key={chain} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={chain}
                          checked={formData.supported_chains.includes(chain)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                supported_chains: [...prev.supported_chains, chain]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                supported_chains: prev.supported_chains.filter(c => c !== chain)
                              }));
                            }
                          }}
                          className="rounded"
                        />
                        <Label htmlFor={chain} className="text-sm font-medium capitalize">
                          {chain}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="expires">Expires In</Label>
                  <select
                    id="expires"
                    value={formData.expires_in_days}
                    onChange={(e) => setFormData(prev => ({ ...prev, expires_in_days: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="7">7 days</option>
                    <option value="30">30 days</option>
                    <option value="90">90 days</option>
                    <option value="365">1 year</option>
                  </select>
                </div>

                <div className="flex gap-2">
                  <Button onClick={createPaymentLink} disabled={creating}>
                    {creating ? 'Creating...' : 'Create Payment Link'}
                  </Button>
                  <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Links List */}
          <Card>
            <CardHeader>
              <CardTitle>Your Payment Links</CardTitle>
              <CardDescription>
                Manage and share your multi-chain payment links
              </CardDescription>
            </CardHeader>
            <CardContent>
              {paymentLinks.length === 0 ? (
                <div className="text-center py-12">
                  <Link className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Links Yet</h3>
                  <p className="text-gray-600 mb-6">
                    Create your first payment link to start accepting multi-chain payments
                  </p>
                  <Button onClick={() => setShowCreateForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Payment Link
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {paymentLinks.map((link) => (
                    <div
                      key={link.id}
                      className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold">{link.title}</h3>
                            <Badge variant={link.status === 'active' ? 'default' : 'secondary'}>
                              {link.status}
                            </Badge>
                            <span className="text-lg font-bold text-green-600">
                              {formatAmount(link.amount, link.currency)}
                            </span>
                          </div>
                          {link.description && (
                            <p className="text-sm text-gray-600 mb-2">{link.description}</p>
                          )}
                          <div className="text-sm text-gray-500 space-y-1">
                            <p>
                              <span className="font-medium">Created:</span> {formatDate(link.created_at)}
                            </p>
                            {link.expires_at && (
                              <p>
                                <span className="font-medium">Expires:</span> {formatDate(link.expires_at)}
                              </p>
                            )}
                            <p>
                              <span className="font-medium">Chains:</span> {link.supported_chains?.join(', ')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyLinkToClipboard(link.link_url)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(link.link_url, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PaymentGatewayPaymentLinks;
