/**
 * Demo Payment Page
 * 
 * Shows what customers see when they click a payment link in demo mode
 */

import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  CreditCard,
  Shield,
  Zap,
  CheckCircle,
  Clock,
  Copy,
  ArrowLeft,
  Wallet,
  QrCode
} from 'lucide-react';

const DemoPaymentPage: React.FC = () => {
  const { payment_intent_id } = useParams();
  const [currentStep, setCurrentStep] = useState<'select' | 'details' | 'success'>('select');
  const [selectedCrypto, setSelectedCrypto] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Mock payment data based on the payment intent ID
  const [paymentData] = useState({
    id: payment_intent_id || 'pi_demo_123',
    amount: 2999, // $29.99 in cents
    currency: 'USD',
    description: 'Premium Subscription',
    merchant_name: 'Demo Business',
    accepted_cryptocurrencies: ['SOL', 'USDC', 'ETH', 'BTC']
  });

  const getCryptoIcon = (crypto: string) => {
    const icons = {
      'SOL': '◎',
      'USDC': '💵',
      'ETH': 'Ξ',
      'BTC': '₿'
    };
    return icons[crypto as keyof typeof icons] || '🪙';
  };

  const getCryptoName = (crypto: string) => {
    const names = {
      'SOL': 'Solana',
      'USDC': 'USD Coin',
      'ETH': 'Ethereum',
      'BTC': 'Bitcoin'
    };
    return names[crypto as keyof typeof names] || crypto;
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const handleCryptoSelection = (crypto: string) => {
    setSelectedCrypto(crypto);
    setCurrentStep('details');
  };

  const handlePayment = async () => {
    setLoading(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setCurrentStep('success');
    setLoading(false);
    
    toast({
      title: "Demo Payment Completed! 🎉",
      description: "This is a demo transaction - no real payment was processed",
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Address copied to clipboard",
    });
  };

  // Mock wallet address and QR code
  const mockWalletAddress = "DemoWallet123...xyz789";
  const mockCryptoAmount = selectedCrypto === 'SOL' ? '0.1234' : 
                          selectedCrypto === 'USDC' ? '29.99' :
                          selectedCrypto === 'ETH' ? '0.0123' : '0.00045';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Complete Your Payment
            </h1>
            <p className="text-gray-600">
              Secure cryptocurrency payment powered by our gateway
            </p>
          </div>

          {/* Payment Steps */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'select' ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'
              }`}>
                {currentStep === 'select' ? '1' : <CheckCircle className="h-5 w-5" />}
              </div>
              <div className={`w-16 h-1 mx-2 ${
                currentStep !== 'select' ? 'bg-green-600' : 'bg-gray-200'
              }`} />
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'details' ? 'bg-blue-600 text-white' : 
                currentStep === 'success' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                {currentStep === 'success' ? <CheckCircle className="h-5 w-5" /> : '2'}
              </div>
              <div className={`w-16 h-1 mx-2 ${
                currentStep === 'success' ? 'bg-green-600' : 'bg-gray-200'
              }`} />
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'success' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                {currentStep === 'success' ? <CheckCircle className="h-5 w-5" /> : '3'}
              </div>
            </div>
          </div>

          {/* Payment Details Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Payment Details</span>
                <Badge variant="outline">Demo Mode</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Merchant:</span> {paymentData.merchant_name}
                </div>
                <div>
                  <span className="font-medium">Amount:</span> {formatAmount(paymentData.amount, paymentData.currency)}
                </div>
                <div>
                  <span className="font-medium">Description:</span> {paymentData.description}
                </div>
                <div>
                  <span className="font-medium">Payment ID:</span> {paymentData.id}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 1: Select Cryptocurrency */}
          {currentStep === 'select' && (
            <Card>
              <CardHeader>
                <CardTitle>Choose Payment Method</CardTitle>
                <CardDescription>
                  Select your preferred cryptocurrency
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {paymentData.accepted_cryptocurrencies.map((crypto) => (
                    <Button
                      key={crypto}
                      variant="outline"
                      className="h-16 flex items-center justify-between p-4 hover:border-blue-300"
                      onClick={() => handleCryptoSelection(crypto)}
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{getCryptoIcon(crypto)}</span>
                        <div className="text-left">
                          <div className="font-semibold">{crypto}</div>
                          <div className="text-sm text-gray-600">{getCryptoName(crypto)}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">~{mockCryptoAmount} {crypto}</div>
                        <div className="text-sm text-gray-600">≈ {formatAmount(paymentData.amount, paymentData.currency)}</div>
                      </div>
                    </Button>
                  ))}
                </div>

                <div className="flex items-center justify-center gap-4 text-xs text-gray-500 pt-4">
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    <span>Secure</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    <span>Fast</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-3 w-3" />
                    <span>No Fees</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Payment Details */}
          {currentStep === 'details' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-2xl">{getCryptoIcon(selectedCrypto)}</span>
                  Pay with {selectedCrypto}
                </CardTitle>
                <CardDescription>
                  Send exactly {mockCryptoAmount} {selectedCrypto} to complete your payment
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Payment Amount */}
                <div className="text-center p-6 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-3xl font-bold text-blue-900 mb-2">
                    {mockCryptoAmount} {selectedCrypto}
                  </div>
                  <div className="text-sm text-blue-700">
                    ≈ {formatAmount(paymentData.amount, paymentData.currency)}
                  </div>
                </div>

                {/* Wallet Address */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Send to this address:
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 p-3 bg-gray-50 border border-gray-300 rounded-lg font-mono text-sm">
                      {mockWalletAddress}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(mockWalletAddress)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* QR Code Placeholder */}
                <div className="text-center">
                  <div className="inline-block p-6 bg-white border-2 border-gray-300 rounded-lg">
                    <QrCode className="h-32 w-32 text-gray-400" />
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    Scan QR code with your wallet app
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={handlePayment}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Processing Payment...
                      </>
                    ) : (
                      <>
                        <Wallet className="h-4 w-4 mr-2" />
                        I've Sent the Payment
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep('select')}
                    className="w-full"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Choose Different Crypto
                  </Button>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <p className="text-sm text-yellow-800">
                    <strong>Demo Note:</strong> This is a demonstration. No real cryptocurrency will be sent or received.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Success */}
          {currentStep === 'success' && (
            <Card>
              <CardContent className="text-center py-12">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Payment Successful!
                </h2>
                
                <p className="text-gray-600 mb-8">
                  Your demo payment has been processed successfully. In a real transaction, 
                  you would receive a confirmation email and the merchant would be notified.
                </p>

                <div className="bg-green-50 p-6 rounded-lg border border-green-200 mb-8">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Transaction ID:</span><br />
                      demo_tx_{Date.now()}
                    </div>
                    <div>
                      <span className="font-medium">Amount Paid:</span><br />
                      {mockCryptoAmount} {selectedCrypto}
                    </div>
                    <div>
                      <span className="font-medium">USD Equivalent:</span><br />
                      {formatAmount(paymentData.amount, paymentData.currency)}
                    </div>
                    <div>
                      <span className="font-medium">Status:</span><br />
                      <Badge className="bg-green-600">Confirmed</Badge>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={() => window.close()}
                  className="mr-4"
                >
                  Close Window
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/demo'}
                >
                  Back to Demo
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <p>Powered by Your Crypto Payment Gateway</p>
            <p>This is a demo environment - no real transactions are processed</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoPaymentPage;
