/**
 * Crypto Payments SDK
 * 
 * JavaScript SDK for integrating crypto payments into any website
 * Usage: <script src="https://yourplatform.com/crypto-payments-sdk.js"></script>
 */

(function(window) {
  'use strict';

  // SDK Configuration
  const SDK_VERSION = '1.0.0';
  const API_BASE_URL = 'https://api.yourplatform.com/v1';
  
  class CryptoPayments {
    constructor(apiKey, options = {}) {
      this.apiKey = apiKey;
      this.options = {
        baseUrl: options.baseUrl || API_BASE_URL,
        theme: options.theme || 'light',
        primaryColor: options.primaryColor || '#3b82f6',
        ...options
      };
      
      this.loadCSS();
    }

    // Load required CSS styles
    loadCSS() {
      if (document.getElementById('crypto-payments-css')) return;
      
      const css = `
        .crypto-payment-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
        }
        
        .crypto-payment-widget {
          background: white;
          border-radius: 12px;
          padding: 24px;
          max-width: 400px;
          width: 90%;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .crypto-payment-button {
          background: ${this.options.primaryColor};
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s;
        }
        
        .crypto-payment-button:hover {
          opacity: 0.9;
          transform: translateY(-1px);
        }
        
        .crypto-payment-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        
        .crypto-crypto-option {
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          padding: 16px;
          margin: 8px 0;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 12px;
          transition: all 0.2s;
        }
        
        .crypto-crypto-option:hover {
          border-color: ${this.options.primaryColor};
          background: #f8fafc;
        }
        
        .crypto-amount-input {
          width: 100%;
          padding: 12px;
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          font-size: 18px;
          text-align: center;
          font-weight: 600;
        }
        
        .crypto-step-indicator {
          display: flex;
          justify-content: center;
          margin-bottom: 24px;
        }
        
        .crypto-step {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 8px;
          font-weight: 600;
          font-size: 14px;
        }
        
        .crypto-step.active {
          background: ${this.options.primaryColor};
          color: white;
        }
        
        .crypto-step.inactive {
          background: #e5e7eb;
          color: #6b7280;
        }
      `;
      
      const style = document.createElement('style');
      style.id = 'crypto-payments-css';
      style.textContent = css;
      document.head.appendChild(style);
    }

    // Create a payment intent
    async createPaymentIntent(params) {
      try {
        const response = await fetch(`${this.options.baseUrl}/payment_intents`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('CryptoPayments: Failed to create payment intent', error);
        throw error;
      }
    }

    // Create a checkout button
    createCheckoutButton(config) {
      const button = document.createElement('button');
      button.className = 'crypto-payment-button';
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
        </svg>
        ${config.buttonText || 'Pay with Crypto'}
        <span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 4px; font-size: 12px;">
          ${this.formatAmount(config.amount, config.currency || 'USD')}
        </span>
      `;

      button.addEventListener('click', () => {
        this.openCheckout(config);
      });

      return button;
    }

    // Open checkout modal
    async openCheckout(config) {
      try {
        // Create payment intent
        const paymentIntent = await this.createPaymentIntent({
          amount: config.amount * 100,
          currency: config.currency || 'USD',
          accepted_cryptocurrencies: config.acceptedCryptos || ['SOL', 'USDC', 'ETH'],
          metadata: config.metadata || {}
        });

        // Create and show modal
        this.showPaymentModal(paymentIntent, config);

      } catch (error) {
        console.error('CryptoPayments: Checkout failed', error);
        if (config.onError) {
          config.onError(error.message);
        }
      }
    }

    // Show payment modal
    showPaymentModal(paymentIntent, config) {
      const modal = document.createElement('div');
      modal.className = 'crypto-payment-modal';
      
      modal.innerHTML = `
        <div class="crypto-payment-widget">
          <div style="text-align: center; margin-bottom: 24px;">
            <h2 style="margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">
              ${config.title || 'Crypto Payment'}
            </h2>
            <p style="margin: 0; color: #6b7280; font-size: 14px;">
              ${config.description || 'Complete your payment with cryptocurrency'}
            </p>
          </div>
          
          <div class="crypto-step-indicator">
            <div class="crypto-step active">1</div>
            <div class="crypto-step inactive">2</div>
            <div class="crypto-step inactive">3</div>
          </div>
          
          <div id="payment-content">
            <div style="text-align: center; margin-bottom: 24px;">
              <div style="font-size: 32px; font-weight: 700; margin-bottom: 8px;">
                ${this.formatAmount(paymentIntent.amount / 100, paymentIntent.currency)}
              </div>
              <p style="color: #6b7280; font-size: 14px;">Payment amount</p>
            </div>
            
            <div style="margin-bottom: 24px;">
              <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">
                Choose Payment Method
              </h3>
              <div id="crypto-options">
                ${paymentIntent.accepted_cryptocurrencies.map(crypto => `
                  <div class="crypto-crypto-option" data-crypto="${crypto}">
                    <span style="font-size: 24px;">${this.getCryptoIcon(crypto)}</span>
                    <span style="font-weight: 600;">${crypto}</span>
                  </div>
                `).join('')}
              </div>
            </div>
            
            <div style="display: flex; gap: 12px;">
              <button id="cancel-btn" style="flex: 1; padding: 12px; border: 2px solid #e5e7eb; background: white; border-radius: 8px; cursor: pointer;">
                Cancel
              </button>
              <button id="continue-btn" class="crypto-payment-button" style="flex: 2;" disabled>
                Continue
              </button>
            </div>
          </div>
        </div>
      `;

      // Add event listeners
      const cryptoOptions = modal.querySelectorAll('.crypto-crypto-option');
      const continueBtn = modal.querySelector('#continue-btn');
      const cancelBtn = modal.querySelector('#cancel-btn');
      let selectedCrypto = null;

      cryptoOptions.forEach(option => {
        option.addEventListener('click', () => {
          cryptoOptions.forEach(opt => opt.style.borderColor = '#e5e7eb');
          option.style.borderColor = this.options.primaryColor;
          selectedCrypto = option.dataset.crypto;
          continueBtn.disabled = false;
        });
      });

      continueBtn.addEventListener('click', () => {
        if (selectedCrypto) {
          window.open(paymentIntent.payment_url, '_blank');
          document.body.removeChild(modal);
          
          if (config.onSuccess) {
            config.onSuccess(paymentIntent);
          }
        }
      });

      cancelBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
        if (config.onCancel) {
          config.onCancel();
        }
      });

      // Close on backdrop click
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          document.body.removeChild(modal);
          if (config.onCancel) {
            config.onCancel();
          }
        }
      });

      document.body.appendChild(modal);
    }

    // Utility functions
    formatAmount(amount, currency) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(amount);
    }

    getCryptoIcon(crypto) {
      const icons = {
        'SOL': '◎',
        'USDC': '💵',
        'ETH': 'Ξ',
        'BTC': '₿'
      };
      return icons[crypto] || '🪙';
    }
  }

  // Global API
  window.CryptoPayments = CryptoPayments;

  // Auto-initialize if data attributes are present
  document.addEventListener('DOMContentLoaded', () => {
    const buttons = document.querySelectorAll('[data-crypto-payments]');
    
    buttons.forEach(button => {
      const config = {
        amount: parseFloat(button.dataset.amount),
        currency: button.dataset.currency || 'USD',
        buttonText: button.dataset.buttonText || button.textContent,
        description: button.dataset.description,
        acceptedCryptos: button.dataset.acceptedCryptos ? 
          button.dataset.acceptedCryptos.split(',') : ['SOL', 'USDC', 'ETH']
      };

      const apiKey = button.dataset.apiKey || window.CRYPTO_PAYMENTS_API_KEY;
      if (!apiKey) {
        console.error('CryptoPayments: API key not found');
        return;
      }

      const sdk = new CryptoPayments(apiKey);
      
      button.addEventListener('click', (e) => {
        e.preventDefault();
        sdk.openCheckout(config);
      });
    });
  });

})(window);
