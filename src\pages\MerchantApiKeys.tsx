/**
 * Merchant API Keys Page
 * 
 * Main page for merchants to manage their payment gateway API keys
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import ApiKeyManagement from '@/components/merchant/ApiKeyManagement';
import WebhookManagement from '@/components/merchant/WebhookManagement';
import {
  ArrowLeft,
  Key,
  Code,
  BookOpen,
  ExternalLink,
  Shield,
  Zap,
  Globe,
  CreditCard,
  Webhook
} from 'lucide-react';

interface MerchantAccount {
  id: string;
  business_name: string;
  is_verified: boolean;
}

const MerchantApiKeys: React.FC = () => {
  const [merchant, setMerchant] = useState<MerchantAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    loadMerchantAccount();
  }, [user]);

  const loadMerchantAccount = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('merchant_accounts')
        .select('id, business_name, is_verified')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error loading merchant account:', error);
        toast({
          title: "Error",
          description: "Failed to load merchant account. Please register as a merchant first.",
          variant: "destructive",
        });
        navigate('/merchant-registration');
        return;
      }

      setMerchant(data);
    } catch (error) {
      console.error('Error loading merchant account:', error);
      toast({
        title: "Error",
        description: "Failed to load merchant account",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!merchant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Merchant Account Required</h2>
              <p className="text-gray-600 mb-4">
                You need to register as a merchant to access API keys.
              </p>
              <Button onClick={() => navigate('/merchant-registration')}>
                Register as Merchant
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/merchant-dashboard')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <img
                  src="/solana.png"
                  alt="Logo"
                  className="h-8 w-8"
                />
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">API Keys</h1>
                  <p className="text-sm text-gray-600">{merchant.business_name}</p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!merchant.is_verified && (
                <Badge variant="outline" className="text-amber-600 border-amber-600">
                  Verification Pending
                </Badge>
              )}
              {merchant.is_verified && (
                <Badge className="bg-green-100 text-green-800">
                  Verified Merchant
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="keys" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="keys" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              API Keys
            </TabsTrigger>
            <TabsTrigger value="webhooks" className="flex items-center gap-2">
              <Webhook className="h-4 w-4" />
              Webhooks
            </TabsTrigger>
            <TabsTrigger value="docs" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Documentation
            </TabsTrigger>
            <TabsTrigger value="examples" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Examples
            </TabsTrigger>
          </TabsList>

          {/* API Keys Tab */}
          <TabsContent value="keys" className="space-y-6">
            {!merchant.is_verified && (
              <Card className="border-amber-200 bg-amber-50">
                <CardContent className="pt-6">
                  <div className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-amber-600 mt-0.5" />
                    <div>
                      <h3 className="font-semibold text-amber-800">Verification Required</h3>
                      <p className="text-amber-700 text-sm mt-1">
                        Your merchant account is pending verification. You can create test API keys now, 
                        but live API keys will be available after verification is complete.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <ApiKeyManagement merchantId={merchant.id} />
          </TabsContent>

          {/* Webhooks Tab */}
          <TabsContent value="webhooks" className="space-y-6">
            <WebhookManagement merchantId={merchant.id} />
          </TabsContent>

          {/* Documentation Tab */}
          <TabsContent value="docs" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Zap className="h-8 w-8 text-blue-600" />
                    <div>
                      <CardTitle className="text-lg">Quick Start</CardTitle>
                      <CardDescription>Get started in 5 minutes</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Learn how to integrate crypto payments into your application with our simple API.
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Guide
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Code className="h-8 w-8 text-green-600" />
                    <div>
                      <CardTitle className="text-lg">API Reference</CardTitle>
                      <CardDescription>Complete API documentation</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Detailed documentation for all endpoints, parameters, and response formats.
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Docs
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Globe className="h-8 w-8 text-purple-600" />
                    <div>
                      <CardTitle className="text-lg">SDKs</CardTitle>
                      <CardDescription>Client libraries</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Official SDKs for JavaScript, Python, PHP, and more programming languages.
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Download SDKs
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Features Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Gateway Features</CardTitle>
                <CardDescription>
                  Everything you need to accept crypto payments globally
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <CreditCard className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Multi-Chain Support</h4>
                        <p className="text-sm text-gray-600">
                          Accept payments in SOL, USDC, ETH, and other popular cryptocurrencies
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Zap className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Instant Settlement</h4>
                        <p className="text-sm text-gray-600">
                          Convert crypto to NGN instantly with real-time exchange rates
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Shield className="h-5 w-5 text-purple-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Secure & Compliant</h4>
                        <p className="text-sm text-gray-600">
                          Bank-grade security with full regulatory compliance
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Globe className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Global Reach</h4>
                        <p className="text-sm text-gray-600">
                          Accept payments from customers worldwide, settle locally
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Code className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Developer Friendly</h4>
                        <p className="text-sm text-gray-600">
                          Simple API similar to Stripe, comprehensive documentation
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <BookOpen className="h-5 w-5 text-purple-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Real-time Analytics</h4>
                        <p className="text-sm text-gray-600">
                          Detailed insights into your payment performance and trends
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Examples Tab */}
          <TabsContent value="examples" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Integration Examples</CardTitle>
                <CardDescription>
                  Copy-paste examples to get started quickly
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold mb-2">Create Payment Intent</h4>
                    <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                      <pre className="text-sm">
{`curl -X POST https://api.yourplatform.com/v1/payment_intents \\
  -H "Authorization: Bearer sk_test_..." \\
  -H "Content-Type: application/json" \\
  -d '{
    "amount": 5000,
    "currency": "USD",
    "accepted_cryptocurrencies": ["SOL", "USDC"],
    "settlement_currency": "NGN",
    "metadata": {
      "order_id": "order_123"
    }
  }'`}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">JavaScript/Node.js</h4>
                    <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                      <pre className="text-sm">
{`const crypto = require('@yourplatform/crypto-payments');
crypto.setApiKey('sk_test_...');

const paymentIntent = await crypto.paymentIntents.create({
  amount: 5000,
  currency: 'USD',
  accepted_cryptocurrencies: ['SOL', 'USDC'],
  settlement_currency: 'NGN'
});

console.log(paymentIntent.payment_url);`}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">React Frontend</h4>
                    <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                      <pre className="text-sm">
{`function PaymentButton({ amount }) {
  const handlePayment = async () => {
    const response = await fetch('/create-payment', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ amount })
    });
    
    const { payment_url } = await response.json();
    window.open(payment_url, '_blank');
  };

  return (
    <button onClick={handlePayment}>
      Pay with Crypto
    </button>
  );
}`}
                      </pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MerchantApiKeys;
