/**
 * Integration Guide Page
 * 
 * Shows businesses how to integrate the crypto payment gateway
 * into their websites and applications
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  Code,
  Copy,
  ExternalLink,
  Zap,
  Shield,
  Globe,
  Smartphone,
  Monitor,
  CreditCard,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const IntegrationGuide: React.FC = () => {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const { toast } = useToast();

  const copyToClipboard = (code: string, label: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(label);
    setTimeout(() => setCopiedCode(null), 2000);
    
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const integrationMethods = [
    {
      id: 'javascript',
      title: 'JavaScript SDK',
      description: 'Add crypto payments to any website with our simple SDK',
      icon: Code,
      difficulty: 'Easy',
      time: '5 minutes'
    },
    {
      id: 'api',
      title: 'REST API',
      description: 'Full control with our RESTful API integration',
      icon: Monitor,
      difficulty: 'Medium',
      time: '30 minutes'
    },
    {
      id: 'widgets',
      title: 'Embeddable Widgets',
      description: 'Drop-in payment widgets for quick integration',
      icon: CreditCard,
      difficulty: 'Easy',
      time: '2 minutes'
    },
    {
      id: 'links',
      title: 'Payment Links',
      description: 'Share payment links via email, social media, or messaging',
      icon: ExternalLink,
      difficulty: 'Easy',
      time: '1 minute'
    }
  ];

  const jsSDKCode = `<!-- Include the SDK -->
<script src="https://yourplatform.com/crypto-payments-sdk.js"></script>

<!-- Initialize with your API key -->
<script>
  const cryptoPayments = new CryptoPayments('pk_test_your_api_key_here');
  
  // Create a checkout button
  const checkoutBtn = cryptoPayments.createCheckoutButton({
    amount: 29.99,
    currency: 'USD',
    buttonText: 'Buy Now with Crypto',
    description: 'Premium Subscription',
    acceptedCryptos: ['SOL', 'USDC', 'ETH'],
    onSuccess: (paymentData) => {
      console.log('Payment successful!', paymentData);
      // Redirect to success page or show confirmation
    },
    onError: (error) => {
      console.error('Payment failed:', error);
    }
  });
  
  // Add button to your page
  document.getElementById('checkout-container').appendChild(checkoutBtn);
</script>`;

  const htmlWidgetCode = `<!-- Simple HTML integration -->
<button 
  data-crypto-payments
  data-api-key="pk_test_your_api_key_here"
  data-amount="29.99"
  data-currency="USD"
  data-button-text="Pay with Crypto"
  data-description="Premium Subscription"
  data-accepted-cryptos="SOL,USDC,ETH"
  class="crypto-payment-btn">
  Buy Now - $29.99
</button>

<!-- Include the SDK at the bottom of your page -->
<script src="https://yourplatform.com/crypto-payments-sdk.js"></script>`;

  const apiCode = `// Create a payment intent
const response = await fetch('https://api.yourplatform.com/v1/payment_intents', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer sk_test_your_secret_key_here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: 2999, // Amount in cents
    currency: 'USD',
    accepted_cryptocurrencies: ['SOL', 'USDC', 'ETH'],
    settlement_currency: 'NGN', // Settle in Nigerian Naira
    metadata: {
      order_id: 'order_123',
      customer_email: '<EMAIL>'
    },
    success_url: 'https://yoursite.com/success',
    cancel_url: 'https://yoursite.com/cancel'
  })
});

const paymentIntent = await response.json();

// Redirect customer to payment page
window.location.href = paymentIntent.payment_url;`;

  const webhookCode = `// Handle webhook events (Node.js/Express example)
app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['x-webhook-signature'];
  const payload = req.body;
  
  try {
    // Verify webhook signature
    const event = cryptoPayments.webhooks.constructEvent(payload, sig, 'whsec_your_webhook_secret');
    
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        console.log('Payment succeeded:', paymentIntent.id);
        
        // Fulfill the order
        fulfillOrder(paymentIntent.metadata.order_id);
        break;
        
      case 'payment_intent.payment_failed':
        console.log('Payment failed:', event.data.object.id);
        break;
    }
    
    res.json({received: true});
  } catch (err) {
    console.log('Webhook signature verification failed.', err.message);
    res.status(400).send(\`Webhook Error: \${err.message}\`);
  }
});`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Integration Guide
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Start accepting crypto payments in minutes with our simple integration options
            </p>
            
            <div className="flex items-center justify-center gap-8 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                <span>Bank-grade security</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-600" />
                <span>Lightning fast</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-purple-600" />
                <span>Global reach</span>
              </div>
            </div>
          </div>

          {/* Integration Methods */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {integrationMethods.map((method) => {
              const Icon = method.icon;
              return (
                <Card key={method.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader className="text-center">
                    <Icon className="h-12 w-12 mx-auto mb-4 text-blue-600" />
                    <CardTitle className="text-lg">{method.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {method.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <div className="flex justify-center gap-2 mb-4">
                      <Badge variant="outline" className="text-xs">
                        {method.difficulty}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {method.time}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Code Examples */}
          <Tabs defaultValue="javascript" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="javascript">JavaScript SDK</TabsTrigger>
              <TabsTrigger value="html">HTML Widget</TabsTrigger>
              <TabsTrigger value="api">REST API</TabsTrigger>
              <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            </TabsList>

            <TabsContent value="javascript" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    JavaScript SDK Integration
                  </CardTitle>
                  <CardDescription>
                    The easiest way to add crypto payments to your website
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{jsSDKCode}</code>
                    </pre>
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(jsSDKCode, 'JavaScript SDK code')}
                    >
                      {copiedCode === 'JavaScript SDK code' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-2">What this does:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Loads the crypto payments SDK</li>
                      <li>• Creates a customizable checkout button</li>
                      <li>• Handles payment success and error callbacks</li>
                      <li>• Opens payment modal when button is clicked</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="html" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    HTML Widget Integration
                  </CardTitle>
                  <CardDescription>
                    No JavaScript knowledge required - just add HTML attributes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{htmlWidgetCode}</code>
                    </pre>
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(htmlWidgetCode, 'HTML widget code')}
                    >
                      {copiedCode === 'HTML widget code' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  
                  <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                    <h4 className="font-semibold text-green-900 mb-2">Perfect for:</h4>
                    <ul className="text-sm text-green-800 space-y-1">
                      <li>• Static websites and landing pages</li>
                      <li>• WordPress sites without custom development</li>
                      <li>• Quick prototypes and MVPs</li>
                      <li>• Non-technical team members</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="api" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Monitor className="h-5 w-5" />
                    REST API Integration
                  </CardTitle>
                  <CardDescription>
                    Full control with server-side integration
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{apiCode}</code>
                    </pre>
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(apiCode, 'REST API code')}
                    >
                      {copiedCode === 'REST API code' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  
                  <div className="mt-6 p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <h4 className="font-semibold text-purple-900 mb-2">API Features:</h4>
                    <ul className="text-sm text-purple-800 space-y-1">
                      <li>• Create payment intents programmatically</li>
                      <li>• Set custom metadata and webhooks</li>
                      <li>• Handle complex business logic</li>
                      <li>• Full control over user experience</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="webhooks" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Webhook Integration
                  </CardTitle>
                  <CardDescription>
                    Receive real-time notifications about payment events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{webhookCode}</code>
                    </pre>
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(webhookCode, 'Webhook code')}
                    >
                      {copiedCode === 'Webhook code' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  
                  <div className="mt-6 p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <h4 className="font-semibold text-orange-900 mb-2">Webhook Events:</h4>
                    <ul className="text-sm text-orange-800 space-y-1">
                      <li>• <code>payment_intent.succeeded</code> - Payment completed</li>
                      <li>• <code>payment_intent.payment_failed</code> - Payment failed</li>
                      <li>• <code>payment_intent.created</code> - New payment created</li>
                      <li>• <code>payment_intent.canceled</code> - Payment canceled</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Getting Started */}
          <Card className="mt-12">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Ready to Get Started?</CardTitle>
              <CardDescription>
                Join thousands of businesses already accepting crypto payments
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">1</span>
                  </div>
                  <h3 className="font-semibold mb-2">Register Your Business</h3>
                  <p className="text-sm text-gray-600">
                    Sign up and verify your business account
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-green-600 font-bold">2</span>
                  </div>
                  <h3 className="font-semibold mb-2">Get Your API Keys</h3>
                  <p className="text-sm text-gray-600">
                    Receive test and live API keys instantly
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-purple-600 font-bold">3</span>
                  </div>
                  <h3 className="font-semibold mb-2">Start Accepting Payments</h3>
                  <p className="text-sm text-gray-600">
                    Integrate and start receiving crypto payments
                  </p>
                </div>
              </div>
              
              <div className="flex justify-center gap-4">
                <Button 
                  size="lg"
                  onClick={() => window.location.href = '/payment-gateway-register'}
                  className="flex items-center gap-2"
                >
                  Register Your Business
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <Button 
                  size="lg"
                  variant="outline"
                  onClick={() => window.open('https://docs.yourplatform.com', '_blank')}
                  className="flex items-center gap-2"
                >
                  View Full Documentation
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default IntegrationGuide;
