# 🚀 Crypto Payment Gateway Setup Guide

## Step 1: Setup Database Schema

1. **Go to your Supabase project**: https://supabase.com/dashboard
2. **Open SQL Editor**
3. **Copy and paste the entire contents** of `database/payment_gateway_schema.sql`
4. **Run the SQL** to create all tables and policies

## Step 2: Test the System

### 🎯 **Demo Page (Test Everything)**
- **URL**: `http://localhost:5173/demo`
- **Features**: Interactive demo of all payment features
- **Test**: Payment widgets, checkout buttons, payment links, code examples

### 🏢 **Payment Gateway Registration**
- **URL**: `http://localhost:5173/payment-gateway-register`
- **Features**: 4-step business registration process
- **Test**: Register a business, select integration types, set preferences

### 📚 **Integration Guide**
- **URL**: `http://localhost:5173/integration-guide`
- **Features**: Complete developer documentation
- **Test**: Copy code examples, see integration methods

### 🍽️ **Restaurant Registration (Existing)**
- **URL**: `http://localhost:5173/merchant-register`
- **Features**: QR code merchant registration
- **Test**: Register restaurant, generate QR codes

## Step 3: Key Differences

### **Payment Gateway Merchants** (NEW)
- **Purpose**: Online businesses, e-commerce, SaaS
- **Registration**: `/payment-gateway-register`
- **Features**: API integration, widgets, payment links
- **Settlement**: Crypto or NGN conversion
- **Table**: `payment_gateway_merchants`

### **Restaurant Merchants** (EXISTING)
- **Purpose**: Physical restaurants, in-person payments
- **Registration**: `/merchant-register`
- **Features**: QR code generation, direct crypto payments
- **Settlement**: Direct crypto only
- **Table**: `merchant_accounts`

## Step 4: Test Flow

### **For Payment Gateway Businesses:**

1. **Register Business**:
   - Go to `/payment-gateway-register`
   - Complete 4-step registration
   - Select multiple integration types
   - Choose settlement preferences

2. **Test Integration**:
   - Go to `/demo`
   - Try payment widgets
   - Test checkout buttons
   - Generate payment links
   - Copy code examples

3. **View Documentation**:
   - Go to `/integration-guide`
   - See JavaScript SDK examples
   - Copy HTML widget code
   - Test REST API examples

### **For Restaurant Merchants:**

1. **Register Restaurant**:
   - Go to `/merchant-register`
   - Complete restaurant registration
   - Get QR code for payments

2. **Use Dashboard**:
   - Go to `/merchant-dashboard`
   - View QR code and business info
   - Accept direct crypto payments

## Step 5: Database Tables Created

```sql
-- Payment gateway merchants (separate from restaurants)
payment_gateway_merchants

-- API keys for business integrations
merchant_api_keys

-- Stripe-like payment intents
payment_intents

-- Payment transaction details
payment_methods

-- Webhook endpoints and delivery
merchant_webhook_endpoints
webhook_deliveries

-- Shareable payment links
payment_links

-- Business integration settings
merchant_integration_settings
```

## Step 6: Integration Options

### **1. JavaScript SDK**
```html
<script src="https://yourplatform.com/crypto-payments-sdk.js"></script>
<script>
  const cryptoPayments = new CryptoPayments('pk_test_your_key');
  const button = cryptoPayments.createCheckoutButton({
    amount: 29.99,
    currency: 'USD',
    onSuccess: (data) => console.log('Success!', data)
  });
</script>
```

### **2. HTML Widget**
```html
<button 
  data-crypto-payments
  data-api-key="pk_test_your_key"
  data-amount="29.99"
  data-currency="USD">
  Pay with Crypto
</button>
```

### **3. REST API**
```javascript
const response = await fetch('/api/v1/payment_intents', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer sk_test_your_key' },
  body: JSON.stringify({
    amount: 2999,
    currency: 'USD',
    accepted_cryptocurrencies: ['SOL', 'USDC', 'ETH']
  })
});
```

### **4. Payment Links**
- Create shareable URLs
- Send via email, social media
- No coding required
- Perfect for invoices

## Step 7: Settlement Options

### **Crypto Settlement**
- Keep payments in original cryptocurrency
- Direct wallet transfers
- No conversion fees

### **NGN Settlement**
- Automatic conversion to Nigerian Naira
- Bank account deposits
- Local currency stability

### **Mixed Settlement**
- Choose per transaction
- Percentage splits
- Risk management

## Step 8: Business Types Supported

- **E-commerce Stores**: Product sales, shopping carts
- **SaaS Companies**: Subscriptions, usage billing
- **Freelancers**: Invoice payments, project billing
- **Content Creators**: Course sales, memberships
- **Non-Profits**: Donations, fundraising
- **Any Business**: Custom integration needs

## 🎉 Ready to Test!

1. **Run the database schema** in Supabase
2. **Go to `/demo`** to test all features
3. **Register a business** at `/payment-gateway-register`
4. **Try the integration examples** at `/integration-guide`

The system is now ready for production use! 🚀
