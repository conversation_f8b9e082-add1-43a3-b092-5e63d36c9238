/**
 * Demo Page
 * 
 * Interactive demo to test all payment gateway features
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import PaymentWidget from '@/components/embeddable/PaymentWidget';
import CheckoutButton from '@/components/embeddable/CheckoutButton';
import {
  Play,
  Code,
  CreditCard,
  Link,
  Zap,
  Shield,
  Globe,
  ArrowRight,
  ExternalLink,
  Copy,
  CheckCircle
} from 'lucide-react';

const Demo: React.FC = () => {
  const [demoConfig, setDemoConfig] = useState({
    amount: 29.99,
    currency: 'USD',
    description: 'Premium Subscription',
    acceptedCryptos: ['SOL', 'USDC', 'ETH'],
    theme: 'light' as 'light' | 'dark',
    primaryColor: '#3b82f6'
  });

  const [paymentLinkConfig, setPaymentLinkConfig] = useState({
    title: 'Premium Product',
    amount: 49.99,
    currency: 'USD',
    description: 'High-quality premium product with lifetime access'
  });

  const [generatedLink, setGeneratedLink] = useState('');
  const { toast } = useToast();

  const handlePaymentSuccess = (paymentData: any) => {
    toast({
      title: "Demo Payment Successful! 🎉",
      description: `Payment ID: ${paymentData.id}`,
    });
  };

  const handlePaymentError = (error: string) => {
    toast({
      title: "Demo Payment Error",
      description: error,
      variant: "destructive",
    });
  };

  const generatePaymentLink = () => {
    // Simulate payment link generation
    const linkId = `demo_${Date.now()}`;
    const baseUrl = window.location.origin;
    const link = `${baseUrl}/link/${linkId}`;
    setGeneratedLink(link);
    
    toast({
      title: "Payment Link Generated! 🔗",
      description: "Your demo payment link is ready",
    });
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const jsSDKCode = `<!-- Include the SDK -->
<script src="https://yourplatform.com/crypto-payments-sdk.js"></script>

<script>
  // Initialize with your API key
  const cryptoPayments = new CryptoPayments('pk_test_demo_key');
  
  // Create checkout button
  const button = cryptoPayments.createCheckoutButton({
    amount: ${demoConfig.amount},
    currency: '${demoConfig.currency}',
    description: '${demoConfig.description}',
    acceptedCryptos: ${JSON.stringify(demoConfig.acceptedCryptos)},
    onSuccess: (data) => console.log('Payment successful!', data),
    onError: (error) => console.error('Payment failed:', error)
  });
  
  // Add to page
  document.getElementById('checkout-container').appendChild(button);
</script>`;

  const htmlWidgetCode = `<button 
  data-crypto-payments
  data-api-key="pk_test_demo_key"
  data-amount="${demoConfig.amount}"
  data-currency="${demoConfig.currency}"
  data-description="${demoConfig.description}"
  data-accepted-cryptos="${demoConfig.acceptedCryptos.join(',')}"
  class="crypto-payment-btn">
  ${demoConfig.description} - $${demoConfig.amount}
</button>

<script src="https://yourplatform.com/crypto-payments-sdk.js"></script>`;

  const apiCode = `// Create payment intent
const response = await fetch('/api/v1/payment_intents', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer sk_test_demo_key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: ${Math.round(demoConfig.amount * 100)}, // Amount in cents
    currency: '${demoConfig.currency}',
    accepted_cryptocurrencies: ${JSON.stringify(demoConfig.acceptedCryptos)},
    settlement_currency: 'NGN',
    metadata: {
      product: '${demoConfig.description}',
      demo: true
    }
  })
});

const paymentIntent = await response.json();
console.log('Payment URL:', paymentIntent.payment_url);`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🚀 Crypto Payment Gateway Demo
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Test all features of our crypto payment system
            </p>
            
            <div className="flex items-center justify-center gap-8 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                <span>Secure Demo Environment</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-600" />
                <span>Real-time Testing</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-purple-600" />
                <span>All Features Available</span>
              </div>
            </div>
          </div>

          <Tabs defaultValue="widget" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="widget">Payment Widget</TabsTrigger>
              <TabsTrigger value="button">Checkout Button</TabsTrigger>
              <TabsTrigger value="links">Payment Links</TabsTrigger>
              <TabsTrigger value="code">Code Examples</TabsTrigger>
            </TabsList>

            {/* Payment Widget Demo */}
            <TabsContent value="widget" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle>Widget Configuration</CardTitle>
                    <CardDescription>
                      Customize the payment widget settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="amount">Amount</Label>
                        <Input
                          id="amount"
                          type="number"
                          step="0.01"
                          value={demoConfig.amount}
                          onChange={(e) => setDemoConfig(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="currency">Currency</Label>
                        <select
                          id="currency"
                          value={demoConfig.currency}
                          onChange={(e) => setDemoConfig(prev => ({ ...prev, currency: e.target.value }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="USD">USD</option>
                          <option value="NGN">NGN</option>
                          <option value="EUR">EUR</option>
                          <option value="GBP">GBP</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Input
                        id="description"
                        value={demoConfig.description}
                        onChange={(e) => setDemoConfig(prev => ({ ...prev, description: e.target.value }))}
                      />
                    </div>

                    <div>
                      <Label>Accepted Cryptocurrencies</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {['SOL', 'USDC', 'ETH', 'BTC'].map((crypto) => (
                          <Badge
                            key={crypto}
                            variant={demoConfig.acceptedCryptos.includes(crypto) ? "default" : "outline"}
                            className="cursor-pointer"
                            onClick={() => {
                              const isSelected = demoConfig.acceptedCryptos.includes(crypto);
                              if (isSelected) {
                                setDemoConfig(prev => ({
                                  ...prev,
                                  acceptedCryptos: prev.acceptedCryptos.filter(c => c !== crypto)
                                }));
                              } else {
                                setDemoConfig(prev => ({
                                  ...prev,
                                  acceptedCryptos: [...prev.acceptedCryptos, crypto]
                                }));
                              }
                            }}
                          >
                            {crypto}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Theme</Label>
                        <select
                          value={demoConfig.theme}
                          onChange={(e) => setDemoConfig(prev => ({ ...prev, theme: e.target.value as 'light' | 'dark' }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="light">Light</option>
                          <option value="dark">Dark</option>
                        </select>
                      </div>
                      <div>
                        <Label htmlFor="color">Primary Color</Label>
                        <Input
                          id="color"
                          type="color"
                          value={demoConfig.primaryColor}
                          onChange={(e) => setDemoConfig(prev => ({ ...prev, primaryColor: e.target.value }))}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Live Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle>Live Preview</CardTitle>
                    <CardDescription>
                      See how the widget looks with your settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <PaymentWidget
                      merchantId="demo_merchant"
                      amount={demoConfig.amount}
                      currency={demoConfig.currency}
                      description={demoConfig.description}
                      acceptedCryptos={demoConfig.acceptedCryptos}
                      theme={demoConfig.theme}
                      primaryColor={demoConfig.primaryColor}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Checkout Button Demo */}
            <TabsContent value="button" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Checkout Button Examples</CardTitle>
                    <CardDescription>
                      Different styles and configurations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h4 className="font-semibold mb-3">Default Button</h4>
                      <CheckoutButton
                        merchantId="demo_merchant"
                        amount={demoConfig.amount}
                        currency={demoConfig.currency}
                        description={demoConfig.description}
                        acceptedCryptos={demoConfig.acceptedCryptos}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                      />
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">Large Button</h4>
                      <CheckoutButton
                        merchantId="demo_merchant"
                        amount={demoConfig.amount}
                        currency={demoConfig.currency}
                        description={demoConfig.description}
                        size="lg"
                        buttonText="Buy Now with Crypto"
                        acceptedCryptos={demoConfig.acceptedCryptos}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                      />
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">Outline Button</h4>
                      <CheckoutButton
                        merchantId="demo_merchant"
                        amount={demoConfig.amount}
                        currency={demoConfig.currency}
                        description={demoConfig.description}
                        variant="outline"
                        buttonText="Pay with Cryptocurrency"
                        acceptedCryptos={demoConfig.acceptedCryptos}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                      />
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">New Window Button</h4>
                      <CheckoutButton
                        merchantId="demo_merchant"
                        amount={demoConfig.amount}
                        currency={demoConfig.currency}
                        description={demoConfig.description}
                        buttonText="Pay in New Window"
                        openInNewWindow={true}
                        acceptedCryptos={demoConfig.acceptedCryptos}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Integration Benefits</CardTitle>
                    <CardDescription>
                      Why choose our checkout buttons
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-semibold">Easy Integration</h4>
                          <p className="text-sm text-gray-600">
                            Add to any website with just a few lines of code
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-semibold">Customizable</h4>
                          <p className="text-sm text-gray-600">
                            Match your brand colors and styling
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-semibold">Mobile Optimized</h4>
                          <p className="text-sm text-gray-600">
                            Works perfectly on all devices
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-semibold">Secure</h4>
                          <p className="text-sm text-gray-600">
                            Bank-grade security and encryption
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Payment Links Demo */}
            <TabsContent value="links" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Create Payment Link</CardTitle>
                    <CardDescription>
                      Generate shareable payment links
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="link_title">Title</Label>
                      <Input
                        id="link_title"
                        value={paymentLinkConfig.title}
                        onChange={(e) => setPaymentLinkConfig(prev => ({ ...prev, title: e.target.value }))}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="link_amount">Amount</Label>
                        <Input
                          id="link_amount"
                          type="number"
                          step="0.01"
                          value={paymentLinkConfig.amount}
                          onChange={(e) => setPaymentLinkConfig(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="link_currency">Currency</Label>
                        <select
                          id="link_currency"
                          value={paymentLinkConfig.currency}
                          onChange={(e) => setPaymentLinkConfig(prev => ({ ...prev, currency: e.target.value }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="USD">USD</option>
                          <option value="NGN">NGN</option>
                          <option value="EUR">EUR</option>
                          <option value="GBP">GBP</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="link_description">Description</Label>
                      <Input
                        id="link_description"
                        value={paymentLinkConfig.description}
                        onChange={(e) => setPaymentLinkConfig(prev => ({ ...prev, description: e.target.value }))}
                      />
                    </div>

                    <Button onClick={generatePaymentLink} className="w-full">
                      <Link className="h-4 w-4 mr-2" />
                      Generate Payment Link
                    </Button>

                    {generatedLink && (
                      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <Label className="text-green-800 font-semibold">Generated Link:</Label>
                        <div className="flex items-center gap-2 mt-2">
                          <Input
                            value={generatedLink}
                            readOnly
                            className="text-sm font-mono"
                          />
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(generatedLink, 'Payment link')}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(generatedLink, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Payment Link Benefits</CardTitle>
                    <CardDescription>
                      Perfect for various use cases
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-semibold mb-2">Use Cases:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Email invoices and billing</li>
                          <li>• Social media sales</li>
                          <li>• WhatsApp and messaging</li>
                          <li>• QR codes for offline sales</li>
                          <li>• One-time product sales</li>
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Features:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• No coding required</li>
                          <li>• Mobile-optimized pages</li>
                          <li>• Multiple crypto options</li>
                          <li>• Real-time notifications</li>
                          <li>• Usage analytics</li>
                        </ul>
                      </div>

                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p className="text-sm text-blue-800">
                          <strong>Pro Tip:</strong> Payment links are perfect for businesses without websites or for quick sales via social media!
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Code Examples */}
            <TabsContent value="code" className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>JavaScript SDK</CardTitle>
                    <CardDescription>
                      Copy this code to integrate the payment widget
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                        <code>{jsSDKCode}</code>
                      </pre>
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute top-2 right-2"
                        onClick={() => copyToClipboard(jsSDKCode, 'JavaScript SDK code')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>HTML Widget</CardTitle>
                    <CardDescription>
                      Simple HTML integration with data attributes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                        <code>{htmlWidgetCode}</code>
                      </pre>
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute top-2 right-2"
                        onClick={() => copyToClipboard(htmlWidgetCode, 'HTML widget code')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>REST API</CardTitle>
                    <CardDescription>
                      Server-side integration for full control
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                        <code>{apiCode}</code>
                      </pre>
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute top-2 right-2"
                        onClick={() => copyToClipboard(apiCode, 'REST API code')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          {/* Call to Action */}
          <Card className="mt-12">
            <CardContent className="text-center py-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Ready to Start Accepting Crypto Payments?
              </h2>
              <p className="text-gray-600 mb-8">
                Join thousands of businesses already using our payment gateway
              </p>
              <div className="flex justify-center gap-4">
                <Button 
                  size="lg"
                  onClick={() => window.location.href = '/payment-gateway-register'}
                  className="flex items-center gap-2"
                >
                  Get Started Now
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <Button 
                  size="lg"
                  variant="outline"
                  onClick={() => window.location.href = '/integration-guide'}
                  className="flex items-center gap-2"
                >
                  View Documentation
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Demo;
